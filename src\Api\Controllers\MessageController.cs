using System.Linq;
using HighAgentsApi.Domain.Dto;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Models;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class MessageController : ControllerBase
{
    private readonly IMessageService _messageService;

    public MessageController(IMessageService messageService)
    {
        _messageService = messageService;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] MessageDto message, string leadName)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await _messageService.CreateAsync(message, leadName);

        return Ok(result);
    }

    [HttpGet("agent/{agentId}")]
    public async Task<IActionResult> GetAllByAgent(int agentId)
    {
        var messages = await _messageService.GetAllByAgentIdAsync(agentId);

        if (!messages.Any())
            return NotFound(new { message = "No messages found for this agent" });

        return Ok(messages);
    }

    [HttpGet("agent/{agentId}/role/{role}")]
    public async Task<IActionResult> GetByRole(int agentId, string role)
    {
        var messages = await _messageService.GetByRoleAsync(agentId, role);

        if (!messages.Any())
            return NotFound(new { message = $"No messages found for role '{role}'" });

        return Ok(messages);
    }

    [HttpGet("agent/{agentId}/recent/{count}")]
    public async Task<IActionResult> GetRecentByAgent(int agentId, int count)
    {
        var messages = await _messageService.GetRecentByAgentIdAsync(agentId, count);

        if (!messages.Any())
            return NotFound(new { message = "No recent messages found" });

        return Ok(messages);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var success = await _messageService.DeleteAsync(id);

        if (!success)
            return NotFound(new { message = "Message not found or could not be deleted" });

        return Ok(new { message = "Message deleted successfully" });
    }

    [HttpDelete("agent/{agentId}")]
    public async Task<IActionResult> DeleteAllByAgent(int agentId)
    {
        var success = await _messageService.DeleteAllByAgentIdAsync(agentId);

        if (!success)
            return BadRequest(new { message = "Could not delete messages for this agent" });

        return Ok(new { message = "All messages for the agent deleted successfully" });
    }

    // Novos endpoints baseados no conversationIdentificator
    [HttpGet("conversation/{conversationIdentificator}")]
    public async Task<IActionResult> GetAllByConversation(string conversationIdentificator)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
            return BadRequest(new { message = "ConversationIdentificator is required" });

        var messages = await _messageService.GetAllByConversationAsync(conversationIdentificator);

        if (!messages.Any())
            return NotFound(new { message = "No messages found for this conversation" });

        return Ok(messages);
    }

    [HttpGet("conversation/{conversationIdentificator}/role/{role}")]
    public async Task<IActionResult> GetByRoleAndConversation(
        string conversationIdentificator,
        string role
    )
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
            return BadRequest(new { message = "ConversationIdentificator is required" });

        if (string.IsNullOrWhiteSpace(role))
            return BadRequest(new { message = "Role is required" });

        var messages = await _messageService.GetByRoleAndConversationAsync(
            conversationIdentificator,
            role
        );

        if (!messages.Any())
            return NotFound(
                new { message = $"No messages found for role '{role}' in this conversation" }
            );

        return Ok(messages);
    }

    [HttpGet("conversation/{conversationIdentificator}/recent/{count}")]
    public async Task<IActionResult> GetRecentByConversation(
        string conversationIdentificator,
        int count
    )
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
            return BadRequest(new { message = "ConversationIdentificator is required" });

        if (count <= 0)
            return BadRequest(new { message = "Count must be greater than 0" });

        var messages = await _messageService.GetRecentByConversationAsync(
            conversationIdentificator,
            count
        );

        if (!messages.Any())
            return NotFound(new { message = "No recent messages found for this conversation" });

        return Ok(messages);
    }

    [HttpDelete("conversation/{conversationIdentificator}")]
    public async Task<IActionResult> DeleteAllByConversation(string conversationIdentificator)
    {
        if (string.IsNullOrWhiteSpace(conversationIdentificator))
            return BadRequest(new { message = "ConversationIdentificator is required" });

        var success = await _messageService.DeleteAllByConversationAsync(conversationIdentificator);

        if (!success)
            return BadRequest(new { message = "Could not delete messages for this conversation" });

        return Ok(new { message = "All messages for the conversation deleted successfully" });
    }

    [HttpGet("agent/{agentId}/grouped-by-conversation")]
    public async Task<IActionResult> GetMessagesGroupedByConversation(int agentId)
    {
        if (agentId <= 0)
            return BadRequest(new { message = "AgentId must be greater than 0" });

        try
        {
            var result = await _messageService.GetMessagesGroupedByConversationAsync(agentId);

            if (result.TotalConversations == 0)
                return NotFound(new { message = "No conversations found for this agent" });

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }

    private class ConversationComparer : IEqualityComparer<ConversationModel>
    {
        public bool Equals(ConversationModel? x, ConversationModel? y)
        {
            return x?.ConversationIdentificator == y?.ConversationIdentificator;
        }

        public int GetHashCode(ConversationModel obj)
        {
            return obj.ConversationIdentificator?.GetHashCode() ?? 0;
        }
    }

    [HttpGet("agent/{agentId}/conversations")]
    public async Task<IActionResult> GetConversationIdentificators(int agentId)
    {
        if (agentId <= 0)
            return BadRequest(new { message = "AgentId must be greater than 0" });

        try
        {
            var result = await _messageService.GetConversationsAsync(agentId);

            if (!result.Any())
                return NotFound(new { message = "No conversations found for this agent" });

            return Ok(result.Distinct(new ConversationComparer()));
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Internal server error", error = ex.Message });
        }
    }
}
