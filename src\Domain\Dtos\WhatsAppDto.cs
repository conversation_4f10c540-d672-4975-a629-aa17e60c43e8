using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighAgentsApi.Domain.Dtos
{
    public class SendMessageRequestDto
    {
        [Required]
        public required string Number { get; set; }

        [Required]
        public required string Text { get; set; }

        [Range(0, int.MaxValue)]
        public int Delay { get; set; } = 1000;
    }

    public class CreateInstanceRequestDto
    {
        [Required]
        public required string InstanceName { get; set; }

        [Required]
        public required int AgentId { get; set; }

        public bool Qrcode { get; set; } = true;

        [DefaultValue("WHATSAPP-BAILEYS")]
        [Required]
        public required string Integration { get; set; } = "WHATSAPP-BAILEYS";
    }

    public class WhatsAppResponseDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public object? Data { get; set; }
        public string[]? Errors { get; set; }
    }

    public class QrCodeResponseDto
    {
        public bool Success { get; set; }
        public object? QrCode { get; set; }
        public string? Message { get; set; }
    }

    public class ConnectionStatusResponseDto
    {
        public bool Success { get; set; }
        public object? Status { get; set; }
        public string? Message { get; set; }
    }
}
