using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace HighAgentsApi.Application.Services
{
    public class WhatsAppBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<WhatsAppBackgroundService> _logger;
        private readonly IConfiguration _configuration;
        private readonly List<string> _configuredInstances = new();

        public WhatsAppBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<WhatsAppBackgroundService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("🚀 WhatsApp Background Service iniciado");

            // Carregar instâncias configuradas
            LoadConfiguredInstances();

            // Aguardar um pouco para garantir que os serviços estejam prontos
            await Task.Delay(5000, stoppingToken);

            // Conectar com todas as instâncias configuradas
            await ConnectToConfiguredInstances(stoppingToken);

            // Manter o serviço rodando
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(30000, stoppingToken); // Verificar a cada 30 segundos
                    await CheckConnectionsStatus();
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ Erro no WhatsApp Background Service");
                    await Task.Delay(10000, stoppingToken); // Aguardar 10 segundos antes de tentar novamente
                }
            }

            _logger.LogInformation("🛑 WhatsApp Background Service parado");
        }

        private void LoadConfiguredInstances()
        {
            try
            {
                var instancesSection = _configuration.GetSection("WhatsApp:Instances");
                var instances = instancesSection.Get<List<string>>();

                if (instances != null && instances.Any())
                {
                    _configuredInstances.AddRange(instances);
                    _logger.LogInformation("📋 Instâncias configuradas: {Instances}", string.Join(", ", _configuredInstances));
                }
                else
                {
                    _logger.LogWarning("⚠️ Nenhuma instância configurada no appsettings.json");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao carregar instâncias configuradas");
            }
        }

        private async Task ConnectToConfiguredInstances(CancellationToken stoppingToken)
        {
            if (!_configuredInstances.Any())
            {
                _logger.LogWarning("⚠️ Nenhuma instância para conectar");
                return;
            }

            using var scope = _serviceProvider.CreateScope();
            var evolutionService = scope.ServiceProvider.GetRequiredService<IEvolutionApiService>();

            // Configurar event handler para processar mensagens
            evolutionService.OnEventReceived += async (sender, e) =>
            {
                try
                {
                    using var eventScope = _serviceProvider.CreateScope();
                    var processor = eventScope.ServiceProvider.GetRequiredService<MessageProcessorService>();
                    await processor.ProcessEvolutionEvent(e);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ Erro ao processar evento Evolution API");
                }
            };

            // Conectar com cada instância configurada
            foreach (var instanceName in _configuredInstances)
            {
                try
                {
                    _logger.LogInformation("🔌 Conectando com instância: {InstanceName}", instanceName);

                    var connectionRequest = new WebhookConnectionRequestDto
                    {
                        InstanceName = instanceName,
                        ServerUrl = _configuration["WhatsApp:BaseUrl"] ?? "http://34.138.53.67",
                        ReconnectionDelay = 5000,
                        ReconnectionAttempts = 10,
                        Timeout = 20000
                    };

                    var result = await evolutionService.StartConnectionAsync(connectionRequest);

                    if (result.Success)
                    {
                        _logger.LogInformation("✅ Conectado com sucesso: {InstanceName}", instanceName);
                    }
                    else
                    {
                        _logger.LogError("❌ Falha ao conectar: {InstanceName} - {Error}", instanceName, result.Error);
                    }

                    // Aguardar um pouco entre conexões
                    await Task.Delay(2000, stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ Erro ao conectar com instância: {InstanceName}", instanceName);
                }
            }
        }

        private async Task CheckConnectionsStatus()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var evolutionService = scope.ServiceProvider.GetRequiredService<IEvolutionApiService>();

                var activeConnections = await evolutionService.GetActiveConnectionsAsync();

                _logger.LogInformation("📊 Status das conexões: {Count} ativas", activeConnections.Count);

                foreach (var instance in _configuredInstances)
                {
                    var status = await evolutionService.GetConnectionStatusAsync(instance);
                    _logger.LogDebug("📱 {Instance}: {Status}", instance, status.IsConnected ? "Conectado" : "Desconectado");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao verificar status das conexões");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("🛑 Parando WhatsApp Background Service...");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var evolutionService = scope.ServiceProvider.GetRequiredService<IEvolutionApiService>();

                // Desconectar todas as instâncias
                foreach (var instanceName in _configuredInstances)
                {
                    try
                    {
                        await evolutionService.StopConnectionAsync(instanceName);
                        _logger.LogInformation("🔌 Desconectado: {InstanceName}", instanceName);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "❌ Erro ao desconectar: {InstanceName}", instanceName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao parar WhatsApp Background Service");
            }

            await base.StopAsync(cancellationToken);
        }
    }
}
