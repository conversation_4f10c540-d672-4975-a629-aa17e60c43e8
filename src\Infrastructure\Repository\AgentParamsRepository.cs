using HighCapital.Core.Domain.HighAgents.Entities;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Dtos;
using Microsoft.EntityFrameworkCore;
using HighCapital.Core.Infrastructure.Database;

namespace HighAgentsApi.Infrastructure.Repository
{
    public class AgentParamsRepository : IAgentParamsRepository
    {
        private readonly CoreDbContext _context;
        private readonly DbSet<AgentParams> _dataset;

        public AgentParamsRepository(CoreDbContext context)
        {
            _context = context;
            _dataset = _context.Set<AgentParams>();
        }

        public async Task<IEnumerable<AgentParams>> GetByUserIdAsync(int userId)
        {
            return await _dataset
                .Where(p => p.UserId == userId)
                .ToListAsync();
        }

        public async Task AddAsync(AgentParams entity)
        {
            await _dataset.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task<AgentParams> UpdateAsync(AgentParams entity)
        {
            _dataset.Update(entity);
            await _context.SaveChangesAsync();
            return (await _dataset.FindAsync(entity.Id))!;
        }

        public async Task<AgentParams> DeleteAsync(AgentParams entity)
        {
            _dataset.Remove(entity);
            await _context.SaveChangesAsync();
            return entity;
        }

        public async Task<IEnumerable<AgentParams>> GetAllAsync()
        {
            return await _dataset.ToListAsync();
        }

        public async Task<AgentParams?> GetByIdAsync(int id)
        {
            return await _dataset.FindAsync(id);
        }

        public async Task<PaginatedResultParams<AgentParams>> GetAgentParamsPaginatedAsync(AgentParamsPaginationRequestDto request)
        {
            var query = _dataset.Where(param => param.UserId == request.UserId);

            var totalCount = await query.CountAsync();

            var agentParams = await query
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            return new PaginatedResultParams<AgentParams>
            {
                Data = agentParams,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }

        public async Task<IEnumerable<AgentParams>> SearchAgentParamsByNameAsync(AgentParamsSearchRequestDto request)
        {
            return await _dataset
                .Where(param => param.UserId == request.UserId &&
                               param.ParamName != null && param.ParamName.Contains(request.ParamName))
                .ToListAsync();
        }

        public async Task<AgentParams?> GetAgentParamsByNameAsync(string paramName, int userId)
        {
            return await _dataset
                .FirstOrDefaultAsync(param => param.UserId == userId &&
                                            param.ParamName != null && param.ParamName.ToLower() == paramName.ToLower());
        }
    }
}
