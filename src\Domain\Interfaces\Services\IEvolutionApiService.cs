using HighAgentsApi.Domain.Dtos;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IEvolutionApiService
    {
        Task<WebhookStatusResponseDto> StartConnectionAsync(WebhookConnectionRequestDto request);
        Task<WebhookStatusResponseDto> StopConnectionAsync(string instanceName);
        Task<WebhookStatusResponseDto> GetConnectionStatusAsync(string instanceName);
        Task<List<string>> GetActiveConnectionsAsync();
        
        event EventHandler<EvolutionEventDto> OnEventReceived;
        event EventHandler<string> OnConnectionStatusChanged;
    }
}
