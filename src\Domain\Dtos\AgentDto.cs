using System.ComponentModel.DataAnnotations;
using System.ComponentModel;

namespace HighAgentsApi.Domain.Dtos
{
    public class AgentDto
    {

        [Required]
        [DefaultValue("Bender")]
        public required string Name { get; set; }

        [Required]
        [DefaultValue("um assistente")]
        public required string Context { get; set; }

        [Required]
        [DefaultValue(2)]
        public required int userId { get; set; }

        [DefaultValue(2)]
        public int? agentParamsId { get; set; }

        [DefaultValue("test")]
        public string? InstanceWhatsappName { get; set; }

    }

    public class AgentUpdateDto
    {

        [Required]
        public string? Name { get; set; }

        [Required]
        public string? Context { get; set; }

        [DefaultValue(2)]
        public int? agentParamsId { get; set; }

        [DefaultValue("test")]
        public string? InstanceWhatsappName { get; set; }

    }

    public class AgentPaginationRequestDto
    {
        [DefaultValue(1)]
        public int Page { get; set; } = 1;

        [DefaultValue(10)]
        public int PageSize { get; set; } = 10;

        [DefaultValue(2)]
        public int UserId { get; set; }
    }

    public class AgentSearchRequestDto
    {
        [Required]
        public required string Name { get; set; }

        [DefaultValue(2)]
        public int UserId { get; set; }
    }

    public class AgentResponseDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
        public int UserId { get; set; }
        public int? AgentParamsId { get; set; }
        public string? ParamName { get; set; }
        public string? InstanceWhatsappName { get; set; }
    }

    public class PaginatedResult<T>
    {
        public IEnumerable<T> Data { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => Page < TotalPages;
        public bool HasPreviousPage => Page > 1;
    }
}
