using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using HighAgentsApi.Domain.Dtos;

namespace Api.Infrastructure.OpenIa
{
    public class OpenAIService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly string _model;

        public OpenAIService(string apiKey, string model = "gpt-3.5-turbo")
        {
            if (string.IsNullOrEmpty(apiKey))
                throw new ArgumentNullException(nameof(apiKey), "OpenAI API key cannot be null or empty.");

            _apiKey = apiKey;
            _model = model;
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri("https://api.openai.com/")
            };
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _apiKey);
        }

        public async Task<string> GetResponseAsync(string systemPrompt, IEnumerable<(string Role, string Content)> messages)
        {

            var chatMessages = new List<object>();

            // Adiciona o system só uma vez
            chatMessages.Add(new { role = "system", content = systemPrompt });

            // Adiciona apenas user e assistant do histórico
            chatMessages.AddRange(
                messages.Where(m => m.Role != "system")
                        .Select(m => new { role = m.Role, content = m.Content })
            );

            var requestBody = new
            {
                model = _model,
                messages = chatMessages,
                temperature = 0.7
            };

            var json = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("v1/chat/completions", content);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Erro na API OpenAI: {response.StatusCode} - {errorContent}");
                throw new HttpRequestException($"Erro na API OpenAI: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();


            var gptResponse = JsonSerializer.Deserialize<GptResponse>(responseContent);

            return gptResponse?.choices?.FirstOrDefault()?.message?.content ?? "Sem resposta";
        }
    }
}
