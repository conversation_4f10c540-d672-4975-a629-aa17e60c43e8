using HighCapital.Core.Domain.HighAgents.Entities;
using HighAgentsApi.Domain.Dtos;

namespace HighAgentsApi.Domain.Interfaces.Repository
{
    public interface IAgentRepository
    {
        Task<IEnumerable<Agent>> GetAllAsync();
        Task<Agent?> GetByIdAsync(int id);
        Task AddAsync(Agent entity);
        Task<Agent> UpdateAsync(Agent entity);
        Task<Agent> DeleteAsync(Agent entity);

        Task<IEnumerable<Agent>> GetAgentByUserIdAsync(int userId);
        Task<PaginatedResult<Agent>> GetAgentsPaginatedAsync(AgentPaginationRequestDto request);
        Task<IEnumerable<Agent>> SearchAgentsByNameAsync(AgentSearchRequestDto request);
        Task<Agent?> GetAgentByNameAsync(string name, int userId);
        Task<Agent?> GetByInstanceNameAsync(string instanceName);
    }
}
