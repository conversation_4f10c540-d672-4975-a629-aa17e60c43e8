using HighAgentsApi.Domain.Dto;
using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace HighAgentsApi.Application.Services
{
    public class MessageProcessorService
    {
        private readonly ILogger<MessageProcessorService> _logger;
        private readonly IMessageService _messageService;
        private readonly IWhatsAppService _whatAppService;
        private readonly IAgentRepository _agentRepository;

        // Cache para deduplicação de eventos processados
        private readonly ConcurrentDictionary<string, DateTime> _processedEvents = new();
        private readonly TimeSpan _eventCacheExpiration = TimeSpan.FromMinutes(10);

        public MessageProcessorService(
            ILogger<MessageProcessorService> logger,
            IMessageService messageService,
            IAgentRepository agentRepository,
            IWhatsAppService whatAppService
            )
        {
            _logger = logger;
            _messageService = messageService;
            _agentRepository = agentRepository;
            _whatAppService = whatAppService;
        }

        public async Task ProcessEvolutionEvent(EvolutionEventDto evolutionEvent)
        {
            try
            {
                // Filtrar eventos que não devem ser processados
                if (ShouldIgnoreEvent(evolutionEvent))
                {
                    _logger.LogDebug("🚫 Evento ignorado: {EventName}", evolutionEvent.EventName);
                    return;
                }

                // Gerar chave única para o evento baseada no conteúdo
                var eventKey = GenerateEventKey(evolutionEvent);

                // Verificar se o evento já foi processado recentemente
                if (IsEventAlreadyProcessed(eventKey))
                {
                    _logger.LogDebug("🔄 Evento duplicado ignorado: {EventName}", evolutionEvent.EventName);
                    return;
                }

                // Marcar evento como processado
                MarkEventAsProcessed(eventKey);

                _logger.LogInformation("🔄 Processando evento: {EventName}", evolutionEvent.EventName);

                switch (evolutionEvent.EventName)
                {
                    case "messages.upsert":
                        {
                            using var doc = JsonDocument.Parse(evolutionEvent.Data?.ToString() ?? "{}");
                            var root = doc.RootElement;
                            string? instanceName = null;
                            JsonElement payload;

                            if (root.ValueKind == JsonValueKind.Object && root.TryGetProperty("data", out var dataProp))
                            {
                                instanceName = root.TryGetProperty("instance", out var instProp) && instProp.ValueKind == JsonValueKind.String
                                    ? instProp.GetString()
                                    : null;
                                payload = dataProp;
                            }
                            else
                            {
                                payload = root;
                            }

                            var messageData = payload.Deserialize<MessageEventDto>(new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                            // 🔹 Verifica se não é nulo e se não é mensagem do bot
                            if (messageData?.Key?.FromMe == true || messageData?.Key?.RemoteJid.EndsWith("@g.us") == true)
                                return; // ignora mensagens enviadas pelo bot

                            await ProcessNewMessage(evolutionEvent); // processa normalmente apenas mensagens de usuários
                            break;
                        }
                    case "messages.update":
                        {
                            // Para eventos de update, apenas log - não processar
                            _logger.LogDebug("📝 Evento de atualização de mensagem recebido: {EventName}", evolutionEvent.EventName);
                            break;
                        }
                    // case "message.delete":
                    //     await ProcessMessageDelete(evolutionEvent);
                    //     break;
                    // case "connection.update":
                    //     await ProcessConnectionUpdate(evolutionEvent);
                    //     break;
                    // case "qr.code":
                    //     await ProcessQrCode(evolutionEvent);
                    //     break;
                    default:
                        _logger.LogDebug("Evento não processado: {EventName}", evolutionEvent.EventName);
                        break;
                }
            }

            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao processar evento: {EventName}", evolutionEvent.EventName);
            }
        }

        private async Task ProcessNewMessage(EvolutionEventDto evolutionEvent)
        {
            try
            {
                // Envelope da Evolution API (modo global): { event, instance, data, ... }
                using var doc = JsonDocument.Parse(evolutionEvent.Data?.ToString() ?? "{}");
                var root = doc.RootElement;
                var result = "";
                string? instanceName = null;
                JsonElement payload;

                if (root.ValueKind == JsonValueKind.Object && root.TryGetProperty("data", out var dataProp))
                {
                    instanceName = root.TryGetProperty("instance", out var instProp) && instProp.ValueKind == JsonValueKind.String
                        ? instProp.GetString()
                        : null;
                    payload = dataProp;
                }
                else
                {
                    // fallback para payload direto
                    payload = root;
                }

                var messageData = payload.Deserialize<MessageEventDto>(new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (messageData?.Message?.Conversation != null)
                {
                    _logger.LogInformation("📨 NOVA MENSAGEM RECEBIDA:");
                    _logger.LogInformation("   📱 De: {RemoteJid}", messageData.Key.RemoteJid);
                    //  _logger.LogInformation("   💬 Texto: {Text}", messageData.Message.Conversation);
                    //  _logger.LogInformation("   ⏰ Timestamp: {Timestamp}",
                    //     DateTimeOffset.FromUnixTimeSeconds(messageData.MessageTimestamp));
                    //  _logger.LogInformation("   🔄 De mim: {FromMe}", messageData.Key.FromMe);
                    if (!string.IsNullOrWhiteSpace(instanceName))
                    {
                        _logger.LogInformation("   🏷️ Instância: {Instance}", instanceName);
                    }

                    if (!string.IsNullOrWhiteSpace(instanceName))
                    {
                        var agent = await _agentRepository.GetByInstanceNameAsync(instanceName);
                        if (agent != null)
                        {
                            var dto = new MessageDto
                            {
                                AgentId = agent.Id,
                                ConversationIdentificator = messageData.Key.RemoteJid.Replace("@s.whatsapp.net", string.Empty),
                                Content = messageData.Message.Conversation,
                                Role = "user",
                                Username = messageData.PushName,
                                Timestamp = DateTime.UtcNow
                            };

                            var leadName = payload.TryGetProperty("pushName", out var pushNameEl) && pushNameEl.ValueKind == JsonValueKind.String
                                ? pushNameEl.GetString() ?? string.Empty
                                : string.Empty;

                            try
                            {
                                result = await _messageService.CreateAsync(dto, leadName);
                                _logger.LogInformation("💾 Mensagem mapeada e salva para AgentId {AgentId}", agent.Id);
                            }
                            catch (Exception exCreate)
                            {
                                _logger.LogError(exCreate, "Erro ao salvar mensagem via MessageService para AgentId {AgentId}", agent.Id);
                            }
                        }
                        else
                        {
                            _logger.LogWarning("⚠️ Nenhum agente encontrado com InstanceName = {Instance}", instanceName);
                        }
                    }
                }

                // Só enviar mensagem se houver uma resposta válida
                if (!string.IsNullOrWhiteSpace(result))
                {
                    var message = new SendMessageRequestDto
                    {
                        Text = result,
                        Number = messageData?.Key?.RemoteJid ?? string.Empty
                    };
                    var instance = instanceName ?? string.Empty;
                    _logger.LogInformation("📤 Enviando resposta: {Text}", result);

                    await _whatAppService.SendMessageAsync(instance, message);
                }
                else
                {
                    _logger.LogDebug("🔇 Nenhuma resposta gerada para a mensagem");
                }

                return;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao processar nova mensagem");
                return;
            }
        }

        private Task ProcessMessageUpdate(EvolutionEventDto evolutionEvent)
        {
            try
            {
                _logger.LogInformation("📝 Mensagem atualizada: {Data}", evolutionEvent.Data);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao processar atualização de mensagem");
                return Task.CompletedTask;
            }
        }

        private Task ProcessMessageDelete(EvolutionEventDto evolutionEvent)
        {
            try
            {
                _logger.LogInformation("🗑️ Mensagem deletada: {Data}", evolutionEvent.Data);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao processar exclusão de mensagem");
                return Task.CompletedTask;
            }
        }

        private Task ProcessConnectionUpdate(EvolutionEventDto evolutionEvent)
        {
            try
            {
                var connectionData = JsonSerializer.Deserialize<ConnectionEventDto>(
                    evolutionEvent.Data?.ToString() ?? "{}");

                _logger.LogInformation("🔌 Status de conexão atualizado:");
                _logger.LogInformation("   📱 Instância: {Instance}", connectionData?.Instance);
                _logger.LogInformation("   🔄 Estado: {State}", connectionData?.State);
                _logger.LogInformation("   📊 Status: {Status}", connectionData?.Status);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao processar atualização de conexão");
                return Task.CompletedTask;
            }
        }

        private Task ProcessQrCode(EvolutionEventDto evolutionEvent)
        {
            try
            {
                var qrData = JsonSerializer.Deserialize<QrCodeEventDto>(
                    evolutionEvent.Data?.ToString() ?? "{}");

                _logger.LogInformation("📱 QR Code atualizado:");
                _logger.LogInformation("   📱 Instância: {Instance}", qrData?.Instance);
                _logger.LogInformation("   🔗 QR Code: {QrCode}", qrData?.QrCode);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao processar QR Code");
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// Verifica se o evento deve ser ignorado
        /// </summary>
        private bool ShouldIgnoreEvent(EvolutionEventDto evolutionEvent)
        {
            // Lista de eventos que devem ser ignorados
            var ignoredEvents = new[]
            {
                "send.message",           // Mensagens enviadas pelo bot
                "messages.update",        // Atualizações de status de mensagem
                "message.update",         // Atualizações de mensagem
                "message.delete",         // Exclusões de mensagem
                "connection.update",      // Atualizações de conexão
                "qr.updated",            // QR Code atualizado
                "qr.code",               // QR Code
                "status.instance",       // Status da instância
                "status.find",           // Busca de status
                "webhook.message",       // Webhook de mensagem
                "webhook.status",        // Webhook de status
                "call.upsert",           // Chamadas
                "call.update",           // Atualizações de chamada
                "labels.edit",           // Edição de labels
                "labels.association",    // Associação de labels
                "presence.update",       // Atualizações de presença
                "chats.update",          // Atualizações de chat
                "contacts.update"        // Atualizações de contatos
            };

            return ignoredEvents.Contains(evolutionEvent.EventName);
        }

        /// <summary>
        /// Gera uma chave única para o evento baseada no conteúdo para deduplicação
        /// </summary>
        private string GenerateEventKey(EvolutionEventDto evolutionEvent)
        {
            try
            {
                using var doc = JsonDocument.Parse(evolutionEvent.Data?.ToString() ?? "{}");
                var root = doc.RootElement;

                // Extrair informações únicas do evento
                var eventName = evolutionEvent.EventName;
                var instance = root.TryGetProperty("instance", out var instProp) ? instProp.GetString() : "";
                var timestamp = root.TryGetProperty("date_time", out var timeProp) ? timeProp.GetString() : "";

                // Para eventos de mensagem (messages.upsert), usar messageId como identificador único
                if (root.TryGetProperty("data", out var dataProp) && dataProp.ValueKind == JsonValueKind.Object)
                {
                    // Para mensagens com messageId
                    if (dataProp.TryGetProperty("messageId", out var messageIdProp))
                    {
                        var messageId = messageIdProp.GetString();
                        var remoteJid = dataProp.TryGetProperty("remoteJid", out var remoteJidProp) ? remoteJidProp.GetString() : "";
                        return $"{eventName}:{instance}:{messageId}:{remoteJid}";
                    }

                    // Para mensagens com key (messages.upsert) - usar apenas key.id e remoteJid
                    if (dataProp.TryGetProperty("key", out var keyProp) && keyProp.TryGetProperty("id", out var keyIdProp))
                    {
                        var keyId = keyIdProp.GetString();
                        var remoteJid = keyProp.TryGetProperty("remoteJid", out var remoteJidProp) ? remoteJidProp.GetString() : "";
                        var fromMe = keyProp.TryGetProperty("fromMe", out var fromMeProp) ? fromMeProp.GetBoolean() : false;
                        // Para messages.upsert, usar apenas keyId e remoteJid para evitar duplicação por timestamp
                        return $"{eventName}:{instance}:{keyId}:{remoteJid}:{fromMe}";
                    }

                    // Para eventos com keyId (como messages.update)
                    if (dataProp.TryGetProperty("keyId", out var keyIdProp2))
                    {
                        var keyId = keyIdProp2.GetString();
                        var remoteJid = dataProp.TryGetProperty("remoteJid", out var remoteJidProp2) ? remoteJidProp2.GetString() : "";
                        return $"{eventName}:{instance}:{keyId}:{remoteJid}";
                    }
                }

                // Para eventos com data como array (chats.update, contacts.update)
                if (root.TryGetProperty("data", out var dataArrayProp) && dataArrayProp.ValueKind == JsonValueKind.Array)
                {
                    var dataHash = dataArrayProp.GetRawText().GetHashCode();
                    return $"{eventName}:{instance}:{timestamp}:{dataHash}";
                }

                // Para outros eventos, usar timestamp e dados principais
                var eventTimestamp = !string.IsNullOrEmpty(timestamp) ? timestamp : DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                var dataHash2 = evolutionEvent.Data?.GetHashCode().ToString() ?? "0";
                return $"{eventName}:{instance}:{eventTimestamp}:{dataHash2}";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao gerar chave do evento, usando fallback");
                return $"{evolutionEvent.EventName}:{evolutionEvent.Timestamp:yyyy-MM-ddTHH:mm:ss.fffZ}:{evolutionEvent.Data?.GetHashCode()}";
            }
        }

        /// <summary>
        /// Verifica se o evento já foi processado recentemente
        /// </summary>
        private bool IsEventAlreadyProcessed(string eventKey)
        {
            if (_processedEvents.TryGetValue(eventKey, out var processedTime))
            {
                // Se o evento foi processado recentemente (dentro do tempo de expiração)
                if (DateTime.UtcNow - processedTime < _eventCacheExpiration)
                {
                    return true;
                }
                else
                {
                    // Remover evento expirado do cache
                    _processedEvents.TryRemove(eventKey, out _);
                }
            }

            return false;
        }

        /// <summary>
        /// Marca o evento como processado
        /// </summary>
        private void MarkEventAsProcessed(string eventKey)
        {
            _processedEvents[eventKey] = DateTime.UtcNow;

            // Limpeza periódica do cache para evitar vazamentos de memória
            if (_processedEvents.Count > 1000)
            {
                CleanupExpiredEvents();
            }
        }

        /// <summary>
        /// Remove eventos expirados do cache
        /// </summary>
        private void CleanupExpiredEvents()
        {
            var expiredKeys = _processedEvents
                .Where(kvp => DateTime.UtcNow - kvp.Value > _eventCacheExpiration)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _processedEvents.TryRemove(key, out _);
            }

            _logger.LogDebug("🧹 Limpeza do cache de eventos: {RemovedCount} eventos expirados removidos", expiredKeys.Count);
        }
    }
}
