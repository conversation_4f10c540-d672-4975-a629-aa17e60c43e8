using HighCapital.Core.Domain.HighAgents.Entities;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Dtos;
using Microsoft.EntityFrameworkCore;
using HighCapital.Core.Infrastructure.Database;

namespace HighAgentsApi.Infrastructure.Repository
{
    public class AgentRepository : IAgentRepository
    {
        private readonly CoreDbContext _context;
        private readonly DbSet<Agent> _dataset;

        public AgentRepository(CoreDbContext context)
        {
            _context = context;
            _dataset = _context.Set<Agent>();
        }

        public async Task<IEnumerable<Agent>> GetAgentByUserIdAsync(int userId)
        {
            return await _dataset.Where(agent => agent.UserId == userId).ToListAsync();
        }

        public async Task AddAsync(Agent entity)
        {
            await _dataset.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task<Agent> UpdateAsync(Agent entity)
        {
            _dataset.Update(entity);
            await _context.SaveChangesAsync();

            return (await _dataset.FindAsync(entity.Id))!;
        }


        public async Task<Agent> DeleteAsync(Agent entity)
        {
            _dataset.Remove(entity);
            await _context.SaveChangesAsync();
            return entity;
        }

        public async Task<IEnumerable<Agent>> GetAllAsync()
        {
            return await _dataset.ToListAsync();
        }

        public async Task<Agent?> GetByIdAsync(int id)
        {
            return await _dataset.FindAsync(id);
        }

        public async Task<PaginatedResult<Agent>> GetAgentsPaginatedAsync(AgentPaginationRequestDto request)
        {
            var query = _dataset.Where(agent => agent.UserId == request.UserId);

            var totalCount = await query.CountAsync();

            var agents = await query
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            return new PaginatedResult<Agent>
            {
                Data = agents,
                TotalCount = totalCount,
                Page = request.Page,
                PageSize = request.PageSize
            };
        }

        public async Task<IEnumerable<Agent>> SearchAgentsByNameAsync(AgentSearchRequestDto request)
        {
            return await _dataset
                .Where(agent => agent.UserId == request.UserId &&
                               agent.Name.Contains(request.Name))
                .ToListAsync();
        }

        public async Task<Agent?> GetAgentByNameAsync(string name, int userId)
        {
            return await _dataset
                .FirstOrDefaultAsync(agent => agent.UserId == userId &&
                                            agent.Name.ToLower() == name.ToLower());
        }

        public async Task<Agent?> GetByInstanceNameAsync(string instanceName)
        {
            // Usa shadow property InstanceName se não estiver mapeada na entidade
            return await _dataset
               .FirstOrDefaultAsync(agent => agent.InstanceWhatsappName == instanceName &&
                                           agent.InstanceWhatsappName.ToLower() == instanceName.ToLower());
        }
    }
}
