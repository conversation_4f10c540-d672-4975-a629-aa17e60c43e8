using HighAgentsApi.Domain.Models;
using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Repository
{
    public interface IMessageRepository
    {
        Task<IEnumerable<Message>> GetAllAsync();
        Task<Message?> GetByIdAsync(int id);
        Task AddAsync(Message entity);
        Task UpdateAsync(Message entity);
        Task DeleteAsync(Message entity);

        // Todas usando int agora
        Task<IEnumerable<Message>> GetByAgentIdAsync(int agentId);
        Task DeleteByAgentIdAsync(int agentId);
        Task<IEnumerable<Message>> GetByRoleAsync(int agentId, string role);
        Task<IEnumerable<Message>> GetRecentByAgentIdAsync(int agentId, int count);
        Task<IEnumerable<Message>> GetByConversationAsync(
            string conversationIdentificator,
            int agentId
        );

        // Novos métodos baseados apenas no conversationIdentificator
        Task<IEnumerable<Message>> GetByConversationIdentificatorAsync(
            string conversationIdentificator
        );
        Task<IEnumerable<Message>> GetByRoleAndConversationAsync(
            string conversationIdentificator,
            string role
        );
        Task<IEnumerable<Message>> GetRecentByConversationAsync(
            string conversationIdentificator,
            int count
        );
        Task DeleteByConversationAsync(string conversationIdentificator);

        // Método para agrupar mensagens por conversa
        Task<Dictionary<string, IEnumerable<Message>>> GetMessagesGroupedByConversationAsync(
            int agentId
        );

        // Método para buscar apenas os identificadores de conversa
        Task<IEnumerable<ConversationModel>> GetConversationsByAgentIdAsync(int agentId);
    }
}
