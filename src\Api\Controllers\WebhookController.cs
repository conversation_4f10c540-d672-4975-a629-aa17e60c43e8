using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Api.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class WebhookController : ControllerBase
    {
        private readonly IEvolutionApiService _evolutionService;
        private readonly ILogger<WebhookController> _logger;

        public WebhookController(IEvolutionApiService evolutionService, ILogger<WebhookController> logger)
        {
            _evolutionService = evolutionService;
            _logger = logger;
        }

        /// <summary>
        /// Inicia uma conexão WebSocket com a Evolution API para uma instância específica
        /// </summary>
        /// <param name="request">Dados da conexão incluindo nome da instância</param>
        [HttpPost("start-connection")]
        public async Task<IActionResult> StartConnection([FromBody] WebhookConnectionRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                _logger.LogInformation("Iniciando conexão WebSocket para instância: {InstanceName}", request.InstanceName);
                
                var result = await _evolutionService.StartConnectionAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao iniciar conexão WebSocket para instância: {InstanceName}", request.InstanceName);
                return StatusCode(500, new WebhookStatusResponseDto
                {
                    Success = false,
                    InstanceName = request.InstanceName,
                    IsConnected = false,
                    Status = "Error",
                    Error = "Erro interno do servidor",
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Para uma conexão WebSocket específica
        /// </summary>
        /// <param name="instanceName">Nome da instância</param>
        [HttpPost("stop-connection/{instanceName}")]
        public async Task<IActionResult> StopConnection(string instanceName)
        {
            if (string.IsNullOrWhiteSpace(instanceName))
            {
                return BadRequest(new { error = "Nome da instância é obrigatório" });
            }

            try
            {
                _logger.LogInformation("Parando conexão WebSocket para instância: {InstanceName}", instanceName);
                
                var result = await _evolutionService.StopConnectionAsync(instanceName);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao parar conexão WebSocket para instância: {InstanceName}", instanceName);
                return StatusCode(500, new WebhookStatusResponseDto
                {
                    Success = false,
                    InstanceName = instanceName,
                    IsConnected = false,
                    Status = "Error",
                    Error = "Erro interno do servidor",
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Verifica o status de uma conexão WebSocket específica
        /// </summary>
        /// <param name="instanceName">Nome da instância</param>
        [HttpGet("status/{instanceName}")]
        public async Task<IActionResult> GetConnectionStatus(string instanceName)
        {
            if (string.IsNullOrWhiteSpace(instanceName))
            {
                return BadRequest(new { error = "Nome da instância é obrigatório" });
            }

            try
            {
                var result = await _evolutionService.GetConnectionStatusAsync(instanceName);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao obter status da conexão para instância: {InstanceName}", instanceName);
                return StatusCode(500, new WebhookStatusResponseDto
                {
                    Success = false,
                    InstanceName = instanceName,
                    IsConnected = false,
                    Status = "Error",
                    Error = "Erro interno do servidor",
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Lista todas as conexões WebSocket ativas
        /// </summary>
        [HttpGet("active-connections")]
        public async Task<IActionResult> GetActiveConnections()
        {
            try
            {
                var activeConnections = await _evolutionService.GetActiveConnectionsAsync();
                
                return Ok(new
                {
                    Success = true,
                    ActiveConnections = activeConnections,
                    Count = activeConnections.Count,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao obter conexões ativas");
                return StatusCode(500, new { error = "Erro interno do servidor" });
            }
        }

        /// <summary>
        /// Endpoint para receber webhooks da Evolution API (opcional)
        /// </summary>
        [HttpPost("receive")]
        public IActionResult ReceiveWebhook([FromBody] object webhookData)
        {
            try
            {
                _logger.LogInformation("📨 Webhook recebido: {Data}", webhookData);
                
                // Aqui você pode processar o webhook recebido
                // Por exemplo, salvar no banco de dados, enviar para fila, etc.
                
                return Ok(new { success = true, message = "Webhook recebido com sucesso" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao processar webhook");
                return StatusCode(500, new { error = "Erro ao processar webhook" });
            }
        }
    }
}
