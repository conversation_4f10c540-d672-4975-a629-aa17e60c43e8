using System.Text;
using Api.Infrastructure.OpenIa;
using HighAgentsApi.Application.Services;
using HighAgentsApi.Domain.Dto;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Models;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Service.Services
{
    public class MessageService : IMessageService
    {
        private readonly IMessageRepository _messageRepository;
        private readonly ILogger<MessageService> _logger;
        private readonly IAgentService _agentService;
        private readonly IAgentParamsService _agentParamsService;
        private readonly PromptContextService _promptContextText;
        private readonly OpenAIService _openAiService;

        public MessageService(
            IMessageRepository messageRepository,
            ILogger<MessageService> logger,
            IAgentService agentService,
            IAgentParamsService agentParamsService,
            PromptContextService promptContextText,
            OpenAIService openAiService
        )
        {
            _messageRepository = messageRepository;
            _logger = logger;
            _agentService = agentService;
            _agentParamsService = agentParamsService;
            _promptContextText = promptContextText;
            _openAiService = openAiService;
        }

        public async Task<IEnumerable<MessageDtoList>> GetAllByAgentIdAsync(int agentId)
        {
            try
            {
                var messages = await _messageRepository.GetByAgentIdAsync(agentId);
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now,
                    MessageIdentificator = m.ConversationIdentificator,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar mensagens para o agente: {AgentId}",
                    agentId
                );
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<IEnumerable<MessageDtoList>> GetByRoleAsync(int agentId, string role)
        {
            try
            {
                var messages = await _messageRepository.GetByRoleAsync(agentId, role);
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now,
                    MessageIdentificator = m.ConversationIdentificator,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar mensagens por role '{Role}' para o agente: {AgentId}",
                    role,
                    agentId
                );
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<IEnumerable<MessageDtoList>> GetRecentByAgentIdAsync(
            int agentId,
            int count
        )
        {
            try
            {
                var messages = await _messageRepository.GetRecentByAgentIdAsync(agentId, count);
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now,
                    MessageIdentificator = m.ConversationIdentificator,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar últimas {Count} mensagens para o agente: {AgentId}",
                    count,
                    agentId
                );
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<string> CreateAsync(MessageDto message, string leadName)
        {
            try
            {
                var agentResult = await _agentService.GetOneById(message.AgentId);

                if (!agentResult.Succeeded || agentResult.Value == null)
                {
                    throw new Exception($"Agente com ID {message.AgentId} não encontrado.");
                }

                var userMessage = new Message
                {
                    AgentId = message.AgentId,
                    Role = message.Role ?? "user",
                    Content = message.Content,
                    ConversationIdentificator = message.ConversationIdentificator,
                    Username = message.Username,
                    CreatedAt = message.Timestamp,
                };

                await _messageRepository.AddAsync(userMessage);

                _logger.LogInformation(
                    "Mensagem criada com sucesso para o agente: {AgentId} pelo usuário",
                    message.AgentId
                );

                var agent = await _agentService.GetOneById(message.AgentId);
                if (!agent.Succeeded || agent.Value == null)
                {
                    throw new Exception($"Agente com ID {message.AgentId} não encontrado.");
                }

                var agentParamsResult = await _agentParamsService.GetOneById(
                    agent.Value.AgentParamsId ?? 0
                );

                if (!agentParamsResult.Succeeded || agentParamsResult.Value == null)
                {
                    _logger.LogInformation(
                        $"Parâmetros do agente {agent.Value.Id} não encontrados."
                    );
                }

                var conversationHistory = await GetByConversationAsync(
                    message.ConversationIdentificator,
                    message.AgentId
                );

                var promptSystem = _promptContextText.CreateHighTicketPrompt(
                    agentParamsResult.Value,
                    leadName
                );

                var aiResponse = await _openAiService.GetResponseAsync(
                    promptSystem,
                    conversationHistory
                );
                var assistantMessage = new Message
                {
                    AgentId = message.AgentId,
                    Content = aiResponse,
                    Role = "assistant",
                    ConversationIdentificator = message.ConversationIdentificator,
                };

                await _messageRepository.AddAsync(assistantMessage);

                return assistantMessage.Content;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao criar mensagem para o agente: {AgentId}",
                    message.AgentId
                );
                throw;
            }
        }

        public async Task<bool> DeleteAsync(int messageId)
        {
            try
            {
                var message = await _messageRepository.GetByIdAsync(messageId);
                if (message == null)
                    return false;

                await _messageRepository.DeleteAsync(message);
                _logger.LogInformation("Mensagem deletada com sucesso. ID: {MessageId}", messageId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar mensagem com ID: {MessageId}", messageId);
                return false;
            }
        }

        public async Task<bool> DeleteAllByAgentIdAsync(int agentId)
        {
            try
            {
                await _messageRepository.DeleteByAgentIdAsync(agentId);
                _logger.LogInformation(
                    "Todas as mensagens deletadas para o agente: {AgentId}",
                    agentId
                );
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao deletar todas as mensagens do agente: {AgentId}",
                    agentId
                );
                return false;
            }
        }

        public async Task<List<(string Role, string Content)>> GetByConversationAsync(
            string conversationIdentificator,
            int agentId
        )
        {
            try
            {
                var history = await _messageRepository.GetByConversationAsync(
                    conversationIdentificator,
                    agentId
                );

                return history.Select(msg => (msg.Role.ToLower(), msg.Content)).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar mensagens por conversa '{ConversationIdentificator}' para o agente: {AgentId}",
                    conversationIdentificator,
                    agentId
                );

                return new List<(string Role, string Content)>();
            }
        }

        // Novos métodos baseados apenas no conversationIdentificator
        public async Task<IEnumerable<MessageDtoList>> GetAllByConversationAsync(
            string conversationIdentificator
        )
        {
            try
            {
                var messages = await _messageRepository.GetByConversationIdentificatorAsync(
                    conversationIdentificator
                );
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now,
                    MessageIdentificator = m.ConversationIdentificator,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar mensagens para a conversa: {ConversationIdentificator}",
                    conversationIdentificator
                );
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<IEnumerable<MessageDtoList>> GetByRoleAndConversationAsync(
            string conversationIdentificator,
            string role
        )
        {
            try
            {
                var messages = await _messageRepository.GetByRoleAndConversationAsync(
                    conversationIdentificator,
                    role
                );
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now,
                    MessageIdentificator = m.ConversationIdentificator,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar mensagens por role '{Role}' para a conversa: {ConversationIdentificator}",
                    role,
                    conversationIdentificator
                );
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<IEnumerable<MessageDtoList>> GetRecentByConversationAsync(
            string conversationIdentificator,
            int count
        )
        {
            try
            {
                var messages = await _messageRepository.GetRecentByConversationAsync(
                    conversationIdentificator,
                    count
                );
                return messages.Select(m => new MessageDtoList
                {
                    Id = m.Id,
                    AgentId = m.AgentId,
                    Role = m.Role,
                    Content = m.Content,
                    Timestamp = DateTime.Now,
                    MessageIdentificator = m.ConversationIdentificator,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar últimas {Count} mensagens para a conversa: {ConversationIdentificator}",
                    count,
                    conversationIdentificator
                );
                return Enumerable.Empty<MessageDtoList>();
            }
        }

        public async Task<bool> DeleteAllByConversationAsync(string conversationIdentificator)
        {
            try
            {
                await _messageRepository.DeleteByConversationAsync(conversationIdentificator);
                _logger.LogInformation(
                    "Todas as mensagens deletadas para a conversa: {ConversationIdentificator}",
                    conversationIdentificator
                );
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao deletar todas as mensagens da conversa: {ConversationIdentificator}",
                    conversationIdentificator
                );
                return false;
            }
        }

        public async Task<MessagesGroupedByConversationDto> GetMessagesGroupedByConversationAsync(
            int agentId
        )
        {
            try
            {
                _logger.LogInformation(
                    "Buscando mensagens agrupadas por conversa para o agente: {AgentId}",
                    agentId
                );

                var groupedMessages =
                    await _messageRepository.GetMessagesGroupedByConversationAsync(agentId);

                if (!groupedMessages.Any())
                {
                    _logger.LogInformation(
                        "Nenhuma mensagem encontrada para o agente: {AgentId}",
                        agentId
                    );
                    return new MessagesGroupedByConversationDto
                    {
                        AgentId = agentId,
                        Conversations = new List<ConversationGroupDto>(),
                        TotalConversations = 0,
                        TotalMessages = 0,
                    };
                }

                var conversations = new List<ConversationGroupDto>();
                var totalMessages = 0;

                foreach (var group in groupedMessages)
                {
                    var messages = group.Value.ToList();
                    var messageDtos = messages
                        .Select(m => new MessageDtoList
                        {
                            Id = m.Id,
                            AgentId = m.AgentId,
                            Role = m.Role,
                            Content = m.Content,
                            Timestamp = DateTime.Now,
                            MessageIdentificator = m.ConversationIdentificator,
                        })
                        .ToList();

                    var messageCount = messages.Count();

                    conversations.Add(
                        new ConversationGroupDto
                        {
                            ConversationIdentificator = group.Key,
                            Messages = messageDtos,
                            MessageCount = messageCount,
                        }
                    );

                    totalMessages += messageCount;
                }

                var result = new MessagesGroupedByConversationDto
                {
                    AgentId = agentId,
                    Conversations = conversations,
                    TotalConversations = conversations.Count,
                    TotalMessages = totalMessages,
                };

                _logger.LogInformation(
                    "Mensagens agrupadas recuperadas para o agente: {AgentId}. Total: {TotalConversations} conversas, {TotalMessages} mensagens",
                    agentId,
                    result.TotalConversations,
                    result.TotalMessages
                );

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao recuperar mensagens agrupadas para o agente: {AgentId}",
                    agentId
                );
                return new MessagesGroupedByConversationDto
                {
                    AgentId = agentId,
                    Conversations = new List<ConversationGroupDto>(),
                    TotalConversations = 0,
                    TotalMessages = 0,
                };
            }
        }

        public async Task<IEnumerable<ConversationModel>> GetConversationsAsync(int agentId)
        {
            try
            {
                _logger.LogInformation(
                    "Buscando identificadores de conversa para o agente: {AgentId}",
                    agentId
                );

                var conversations = await _messageRepository.GetConversationsByAgentIdAsync(
                    agentId
                );

                _logger.LogInformation(
                    "Encontrados {Count} identificadores de conversa para o agente: {AgentId}",
                    conversations.Count(),
                    agentId
                );

                return conversations;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Erro ao buscar identificadores de conversa para o agente: {AgentId}",
                    agentId
                );
                return new List<ConversationModel>();
            }
        }
    }
}
