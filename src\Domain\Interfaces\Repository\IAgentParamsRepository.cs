using HighCapital.Core.Domain.HighAgents.Entities;
using HighAgentsApi.Domain.Dtos;

namespace HighAgentsApi.Domain.Interfaces.Repository
{
    public interface IAgentParamsRepository
    {
        Task<IEnumerable<AgentParams>> GetAllAsync();
        Task<AgentParams?> GetByIdAsync(int id);
        Task AddAsync(AgentParams entity);
        Task<AgentParams> UpdateAsync(AgentParams entity);
        Task<AgentParams> DeleteAsync(AgentParams entity);

        Task<IEnumerable<AgentParams>> GetByUserIdAsync(int userId);

        Task<PaginatedResultParams<AgentParams>> GetAgentParamsPaginatedAsync(AgentParamsPaginationRequestDto request);
        Task<IEnumerable<AgentParams>> SearchAgentParamsByNameAsync(AgentParamsSearchRequestDto request);
        Task<AgentParams?> GetAgentParamsByNameAsync(string paramName, int userId);
    }
}
