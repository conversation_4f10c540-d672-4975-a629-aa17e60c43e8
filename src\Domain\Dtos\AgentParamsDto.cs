using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace HighAgentsApi.Domain.Dtos
{
    public class AgentParamsDto
    {
        [DefaultValue("Default")]
        public string? ParamName { get; set; }

        // Identidade e Persona
        [DefaultValue("High Ticket Solutions")]
        public string? CompanyName { get; set; }

        [DefaultValue("Empresa de soluções digitais focada em alta performance")]
        public string? CompanyDescription { get; set; }

        [DefaultValue("Estratégico e direto")]
        public string? Tone { get; set; }

        [DefaultValue("Agendar reuniões com leads qualificados e gerar valor")]
        public string? Goals { get; set; }

        [DefaultValue("Missão principal: ajudar clientes a escalar negócios")]
        public string? MainMission { get; set; }

        [DefaultValue("Contexto de atuação em funil de vendas e mentoria")]
        public string? ContextDescription { get; set; }

        [DefaultValue("Regras de qualificação: ticket mínimo, perfil decisor")]
        public string? QualificationRules { get; set; }

        // Scripts e Framework do Agente
        [DefaultValue("Fluxo natural, perguntas personalizadas, escuta ativa")]
        public string? ConversationGuidelines { get; set; }

        [DefaultValue("Saudação calorosa com apresentação de autoridade")]
        public string? OpeningScript { get; set; }

        [DefaultValue("Perguntar nome, produto/serviço e maior desafio")]
        public string? PreQualificationQuestions { get; set; }

        [DefaultValue("Identificar dor principal e amplificar impacto")]
        public string? PainAgitationScript { get; set; }

        [DefaultValue("Mostrar limitações de preço e opções tradicionais")]
        public string? PricingAgitationScript { get; set; }

        [DefaultValue("Mostrar limitações de métodos antigos e gerar rapport")]
        public string? TraditionalMethods { get; set; }

        [DefaultValue("Apresentar solução moderna com IA para agendamento")]
        public string? SolutionScript { get; set; }

        [DefaultValue("Calcular ROI e economia com IA High Ticket")]
        public string? ValueGenerationScript { get; set; }

        [DefaultValue("Perguntar ticket médio e conversão de calls")]
        public string? FinalQualificationQuestions { get; set; }

        [DefaultValue("Reforçar oportunidade ou fluxo de desqualificação")]
        public string? OpportunityReinforcementScript { get; set; }

        [DefaultValue("Perguntar transformação desejada pelo lead")]
        public string? EmotionalActivationScript { get; set; }

        [DefaultValue("Apresentar proposta personalizada e agendar")]
        public string? CallToActionScript { get; set; }

        [DefaultValue("Encerrar cordialmente caso não qualifique")]
        public string? DisqualifiedFlowScript { get; set; }

        [DefaultValue("Nunca mais de uma pergunta por vez")]
        public string? RestrictionsAndLimits { get; set; }

        // Agendamento e mensagens automáticas
        [DefaultValue("Qualificar leads perguntando sobre metas e desafios")]
        public string? AskAvailabilityStyle { get; set; }

        [DefaultValue("Confirmar horário, data e local de forma clara")]
        public string? ConfirmationStyle { get; set; }

        [DefaultValue("Adaptar linguagem ao perfil do usuário")]
        public string? UserTone { get; set; }

        [DefaultValue("Sugerir alternativas de horários automaticamente")]
        public string? AlternativeSuggestionStyle { get; set; }

        [DefaultValue("Enviar lembretes por e-mail ou push")]
        public string? ReminderStyle { get; set; }

        [DefaultValue("1 hora antes do compromisso")]
        public string? ReminderTiming { get; set; }

        [DefaultValue("Semanal")]
        public string? RecurrenceStyle { get; set; }

        [DefaultValue("Clique aqui para confirmar seu agendamento")]
        public string? CallToAction { get; set; }

        [DefaultValue("Obrigado por utilizar nosso assistente!")]
        public string? CourtesyMessage { get; set; }

        // Relacionamento com usuário
        [DefaultValue(42)]
        public int? UserId { get; set; }
    }

    public class AgentParamsPaginationRequestDto
    {
        [DefaultValue(1)]
        public int Page { get; set; } = 1;

        [DefaultValue(10)]
        public int PageSize { get; set; } = 10;

        [DefaultValue(2)]
        public int UserId { get; set; }
    }

    public class AgentParamsSearchRequestDto
    {
        [Required]
        public required string ParamName { get; set; }

        [DefaultValue(2)]
        public int UserId { get; set; }
    }

    public class PaginatedResultParams<T>
    {
        public IEnumerable<T> Data { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => Page < TotalPages;
        public bool HasPreviousPage => Page > 1;
    }
}
