using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Api.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    public class WhatsAppController : ControllerBase
    {
        private readonly IWhatsAppService _whatsAppService;

        public WhatsAppController(IWhatsAppService whatsAppService)
        {
            _whatsAppService = whatsAppService;
        }

        /// <summary>
        /// Testa a conexão com o serviço externo do WhatsApp
        /// </summary>
        [HttpGet("test")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                var result = await _whatsAppService.TestConnectionAsync();

                if (!result.Succeeded)
                {
                    return BadRequest(new { errors = result.Errors });
                }

                return Ok(result.Value);
            }
            catch (Exception)
            {
                return StatusCode(500, new { errors = new[] { "Erro interno do servidor" } });
            }
        }

        /// <summary>
        /// Envia uma mensagem via WhatsApp
        /// </summary>
        /// <param name="instanceName">Nome da instância do WhatsApp</param>
        /// <param name="request">Dados da mensagem</param>
        [HttpPost("{instanceName}/send-message")]
        public async Task<IActionResult> SendMessage(string instanceName, [FromBody] SendMessageRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (string.IsNullOrWhiteSpace(instanceName))
            {
                return BadRequest(new { errors = new[] { "Nome da instância é obrigatório" } });
            }

            try
            {
                var result = await _whatsAppService.SendMessageAsync(instanceName, request);

                if (!result.Succeeded)
                {
                    return BadRequest(new { errors = result.Errors });
                }

                return Ok(result.Value);
            }
            catch (Exception)
            {
                return StatusCode(500, new { errors = new[] { "Erro interno do servidor" } });
            }
        }

        /// <summary>
        /// Cria uma nova instância do WhatsApp
        /// </summary>
        /// <param name="request">Dados da instância</param>
        [HttpPost("instance")]
        public async Task<IActionResult> CreateInstance([FromBody] CreateInstanceRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var result = await _whatsAppService.CreateInstanceAsync(request);

                if (!result.Succeeded)
                {
                    return BadRequest(new { errors = result.Errors });
                }

                return Ok(result.Value);
            }
            catch (Exception)
            {
                return StatusCode(500, new { errors = new[] { "Erro interno do servidor" } });
            }
        }

        /// <summary>
        /// Obtém o QR Code para conectar a instância do WhatsApp
        /// </summary>
        /// <param name="instanceName">Nome da instância do WhatsApp</param>
        [HttpGet("{instanceName}/qrcode")]
        public async Task<IActionResult> GetQrCode(string instanceName)
        {
            if (string.IsNullOrWhiteSpace(instanceName))
            {
                return BadRequest(new { errors = new[] { "Nome da instância é obrigatório" } });
            }

            try
            {
                var result = await _whatsAppService.GetQrCodeAsync(instanceName);

                if (!result.Succeeded)
                {
                    return BadRequest(new { errors = result.Errors });
                }

                return Ok(result.Value);
            }
            catch (Exception)
            {
                return StatusCode(500, new { errors = new[] { "Erro interno do servidor" } });
            }
        }

        /// <summary>
        /// Verifica o status de conexão da instância do WhatsApp
        /// </summary>
        /// <param name="instanceName">Nome da instância do WhatsApp</param>
        [HttpGet("{instanceName}/connection-status")]
        public async Task<IActionResult> GetConnectionStatus(string instanceName)
        {
            if (string.IsNullOrWhiteSpace(instanceName))
            {
                return BadRequest(new { errors = new[] { "Nome da instância é obrigatório" } });
            }

            try
            {
                var result = await _whatsAppService.GetConnectionStatusAsync(instanceName);

                if (!result.Succeeded)
                {
                    return BadRequest(new { errors = result.Errors });
                }

                return Ok(result.Value);
            }
            catch (Exception)
            {
                return StatusCode(500, new { errors = new[] { "Erro interno do servidor" } });
            }
        }
    }
}
