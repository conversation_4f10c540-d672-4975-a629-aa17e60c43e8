using System.ComponentModel.DataAnnotations;

namespace HighAgentsApi.Domain.Dtos
{
    public class EvolutionEventDto
    {
        public string EventName { get; set; } = string.Empty;
        public object? Data { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class MessageEventDto
    {
        public MessageKeyDto Key { get; set; } = new();
        public MessageContentDto Message { get; set; } = new();
        public long MessageTimestamp { get; set; }
        public string PushName { get; set; } = string.Empty;
    }

    public class MessageKeyDto
    {
        public string RemoteJid { get; set; } = string.Empty;
        public bool FromMe { get; set; }
        public string Id { get; set; } = string.Empty;
    }

    public class MessageContentDto
    {
        public string? Conversation { get; set; }
        public string MessageType { get; set; } = string.Empty;
    }

    public class ConnectionEventDto
    {
        public string Instance { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }

    public class QrCodeEventDto
    {
        public string Instance { get; set; } = string.Empty;
        public string QrCode { get; set; } = string.Empty;
        public string Base64 { get; set; } = string.Empty;
    }

    public class WebhookConnectionRequestDto
    {
        [Required]
        public required string InstanceName { get; set; }
        
        public string ServerUrl { get; set; } = "http://34.138.53.67";
        public int ReconnectionDelay { get; set; } = 5000;
        public int ReconnectionAttempts { get; set; } = 10;
        public int Timeout { get; set; } = 20000;
    }

    public class WebhookStatusResponseDto
    {
        public bool Success { get; set; }
        public string InstanceName { get; set; } = string.Empty;
        public bool IsConnected { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? Error { get; set; }
    }
}
