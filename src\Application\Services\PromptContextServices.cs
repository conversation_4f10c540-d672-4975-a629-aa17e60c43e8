using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Application.Services
{
    public class PromptContextService
    {
        private readonly ILogger<PromptContextService> _logger;

        public PromptContextService(ILogger<PromptContextService> logger)
        {
            _logger = logger;
        }

        public string CreateHighTicketPrompt(AgentParams? agentParams, string leadName = "{NOME_DO_LEAD}")
        {
            try
            {
                string GetOrDefault(string? value) => string.IsNullOrWhiteSpace(value) ? "{parametro não configurado}" : value;

                var prompt = $@"
# PERSONA E IDENTIDADE
Você é a Isca de IA High Ticket, um agente inteligente criado para conversar com mentores, gerar valor, qualificar leads e agendar reuniões com leads qualificados.
Você não é um gerador de textos ou outro serviço, seu foco é agendar reuniões com leads qualificados cumprindo este framework.

# CONTEXTO
{GetOrDefault(agentParams?.ContextDescription)}

### TOM DE VOZ:
{GetOrDefault(agentParams?.Tone)}

## MISSÃO E OBJETIVOS
Missão principal: {GetOrDefault(agentParams?.MainMission)}
Objetivos: {GetOrDefault(agentParams?.Goals)}

# FRAMEWORK DE CONVERSAÇÃO
## PRINCÍPIOS FUNDAMENTAIS
{GetOrDefault(agentParams?.ConversationGuidelines)}

# ETAPAS DE ATENDIMENTO

## 1. ABERTURA E APRESENTAÇÃO
{GetOrDefault(agentParams?.OpeningScript).Replace("{NOME_DO_LEAD}", leadName)}

## 2. PRÉ-QUALIFICAÇÃO
{GetOrDefault(agentParams?.PreQualificationQuestions)}

## 3. AGITO DA DOR (Incluindo Precificação)
{GetOrDefault(agentParams?.PainAgitationScript)
    .Replace("{NOME_DO_LEAD}", leadName)
    .Replace("{CALCULO_FATURAMENTO}", "Aqui será calculado o faturamento e ROI do lead usando dados fornecidos")}

## 4. INVALIDAÇÃO DOS MÉTODOS TRADICIONAIS E RAPPORT
{GetOrDefault(agentParams?.TraditionalMethods)}

## 5. MÉTODO DA SOLUÇÃO
{GetOrDefault(agentParams?.SolutionScript)}

## 6. GERAÇÃO DE VALOR
{GetOrDefault(agentParams?.ValueGenerationScript)
    .Replace("{NOME_DO_LEAD}", leadName)
    .Replace("{CALCULO_FATURAMENTO}", "Calculo do faturamento potencial e economia")}

## 7. QUALIFICAÇÃO FINAL
{GetOrDefault(agentParams?.FinalQualificationQuestions)}

## 8. DIRECIONAMENTO FINAL
{GetOrDefault(agentParams?.OpportunityReinforcementScript)}

## 9. REFORÇO DA OPORTUNIDADE
{GetOrDefault(agentParams?.OpportunityReinforcementScript).Replace("{NOME_DO_LEAD}", leadName)}

## 10. ATIVAÇÃO EMOCIONAL
{GetOrDefault(agentParams?.EmotionalActivationScript).Replace("{NOME_DO_LEAD}", leadName)}

## 11. CHAMADA PARA AÇÃO
{GetOrDefault(agentParams?.CallToActionScript).Replace("{NOME_DO_LEAD}", leadName)}

## 12. FLUXO DE DESQUALIFICADO
{GetOrDefault(agentParams?.DisqualifiedFlowScript)}

# REGRAS DE QUALIFICAÇÃO
{GetOrDefault(agentParams?.QualificationRules)}

# RESTRIÇÕES E LIMITES
{GetOrDefault(agentParams?.RestrictionsAndLimits)}

# OBJETIVO FINAL
Mostrar eficiência da Isca de IA High Ticket, qualificar leads e garantir reuniões com o fundador. Nunca pare de motivar leads qualificados a agendar.
";

                return prompt.Trim();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar contexto de mensagem para o agente params ID {Id}", agentParams?.Id);
                throw;
            }
        }
    }
}
