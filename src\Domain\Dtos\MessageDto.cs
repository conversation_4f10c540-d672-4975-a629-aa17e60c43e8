using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace HighAgentsApi.Domain.Dto
{
    public class MessageDto
    {
        [JsonIgnore]
        public int? Id { get; set; }

        [JsonIgnore]
        public string? Role { get; set; }

        [Required]
        [DefaultValue(2)]
        public required int AgentId { get; set; }

        [Required]
        [DefaultValue("22996043721")]
        public required string ConversationIdentificator { get; set; }

        [Required]
        [DefaultValue("Olá")]
        public required string Content { get; set; }
        
        [Required]
        public required string Username { get; set; }

        [JsonIgnore]
        public DateTime? Timestamp { get; set; }
    }

    public class MessageDtoList
    {
        [Required]
        public required int Id { get; set; }

        [Required]
        public required string Role { get; set; }

        [Required]
        public required string MessageIdentificator { get; set; }

        [Required]
        public int AgentId { get; set; }

        [Required]
        public required string Content { get; set; }

        [Required]
        public required DateTime Timestamp { get; set; }
    }

    public class ConversationGroupDto
    {
        [Required]
        public required string ConversationIdentificator { get; set; }

        [Required]
        public required IEnumerable<MessageDtoList> Messages { get; set; }

        [Required]
        public int MessageCount { get; set; }
    }

    public class MessagesGroupedByConversationDto
    {
        [Required]
        public required int AgentId { get; set; }

        [Required]
        public required IEnumerable<ConversationGroupDto> Conversations { get; set; }

        [Required]
        public int TotalConversations { get; set; }

        [Required]
        public int TotalMessages { get; set; }
    }
}
