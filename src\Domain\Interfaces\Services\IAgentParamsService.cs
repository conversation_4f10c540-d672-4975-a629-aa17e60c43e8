using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IAgentParamsService
    {
        Task<Result<AgentParams>> GetOneById(int agentParamsId);
        Task<Result<IEnumerable<AgentParams>>> GetAll(int userId);
        Task<Result<AgentParams>> Update(AgentParamsDto agentParams, int agentParamsId);
        Task<Result<AgentParams>> Create(AgentParamsDto agentParams);
        Task<Result<AgentParams>> Delete(int agentParamsId);
        Task<Result<PaginatedResultParams<AgentParams>>> GetAgentParamsPaginated(AgentParamsPaginationRequestDto request);
        Task<Result<IEnumerable<AgentParams>>> SearchAgentParamsByName(AgentParamsSearchRequestDto request);
        Task<Result<AgentParams>> GetAgentParamsByName(string paramName, int userId);
    }
}
