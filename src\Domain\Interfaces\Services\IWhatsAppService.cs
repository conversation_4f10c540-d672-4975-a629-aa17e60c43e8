using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Responses;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IWhatsAppService
    {
        Task<Result<WhatsAppResponseDto>> TestConnectionAsync();
        Task<Result<WhatsAppResponseDto>> SendMessageAsync(string instanceName, SendMessageRequestDto request);
        Task<Result<WhatsAppResponseDto>> CreateInstanceAsync(CreateInstanceRequestDto request);
        Task<Result<QrCodeResponseDto>> GetQrCodeAsync(string instanceName);
        Task<Result<ConnectionStatusResponseDto>> GetConnectionStatusAsync(string instanceName);
    }
}
