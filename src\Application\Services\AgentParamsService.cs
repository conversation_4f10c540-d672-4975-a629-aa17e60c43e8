using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.HighAgents.Entities;
using Microsoft.Extensions.Logging;

namespace HighAgentsApi.Service.Services
{
    public class AgentParamsService : IAgentParamsService
    {
        private readonly IAgentParamsRepository _agentParamsRepository;
        private readonly ILogger<AgentParamsService> _logger;

        public AgentParamsService(
            IAgentParamsRepository agentParamsRepository,
            ILogger<AgentParamsService> logger)
        {
            _agentParamsRepository = agentParamsRepository;
            _logger = logger;
        }

        public async Task<Result<AgentParams>> Create(AgentParamsDto data)
        {
            try
            {
                // Verificar se já existe um AgentParams com o mesmo nome para o usuário
                if (!string.IsNullOrEmpty(data.ParamName) && data.UserId.HasValue)
                {
                    var existingAgentParams = await _agentParamsRepository.GetAgentParamsByNameAsync(data.ParamName, data.UserId.Value);
                    if (existingAgentParams != null)
                    {
                        _logger.LogWarning("Tentativa de criar AgentParams com nome duplicado: {ParamName} para o usuário: {UserId}",
                            data.ParamName, data.UserId);
                        return Result<AgentParams>.Failure(new[] { "Já existe um parâmetro com este nome para o usuário." });
                    }
                }

                var agentParams = new AgentParams
                {
                    ParamName = data.ParamName,
                    CompanyName = data.CompanyName,
                    CompanyDescription = data.CompanyDescription,
                    Tone = data.Tone,
                    Goals = data.Goals,
                    MainMission = data.MainMission,
                    ContextDescription = data.ContextDescription,
                    QualificationRules = data.QualificationRules,
                    ConversationGuidelines = data.ConversationGuidelines,
                    OpeningScript = data.OpeningScript,
                    PreQualificationQuestions = data.PreQualificationQuestions,
                    PainAgitationScript = data.PainAgitationScript,
                    PricingAgitationScript = data.PricingAgitationScript,
                    TraditionalMethods = data.TraditionalMethods,
                    SolutionScript = data.SolutionScript,
                    ValueGenerationScript = data.ValueGenerationScript,
                    FinalQualificationQuestions = data.FinalQualificationQuestions,
                    OpportunityReinforcementScript = data.OpportunityReinforcementScript,
                    EmotionalActivationScript = data.EmotionalActivationScript,
                    CallToActionScript = data.CallToActionScript,
                    DisqualifiedFlowScript = data.DisqualifiedFlowScript,
                    RestrictionsAndLimits = data.RestrictionsAndLimits,
                    AskAvailabilityStyle = data.AskAvailabilityStyle,
                    ConfirmationStyle = data.ConfirmationStyle,
                    UserTone = data.UserTone,
                    AlternativeSuggestionStyle = data.AlternativeSuggestionStyle,
                    ReminderStyle = data.ReminderStyle,
                    ReminderTiming = data.ReminderTiming,
                    RecurrenceStyle = data.RecurrenceStyle,
                    CallToAction = data.CallToAction,
                    CourtesyMessage = data.CourtesyMessage,
                    UserId = data.UserId
                };

                await _agentParamsRepository.AddAsync(agentParams);

                _logger.LogInformation("Parâmetros criados com sucesso para o usuário: {UserId}", data.UserId);
                return Result<AgentParams>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar os parâmetros do agente para o usuário: {UserId}", data.UserId);
                return Result<AgentParams>.Failure(new[] { "Erro ao criar parâmetros." });
            }
        }
        public async Task<Result<AgentParams>> Delete(int id)
        {
            try
            {
                var agentParams = await _agentParamsRepository.GetByIdAsync(id);
                if (agentParams == null)
                {
                    return Result<AgentParams>.Failure(new[] { "Parâmetros não encontrados." });
                }

                var result = await _agentParamsRepository.DeleteAsync(agentParams);

                _logger.LogInformation("Parâmetros deletados com sucesso. ID: {Id}", id);
                return Result<AgentParams>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao deletar parâmetros com ID: {Id}", id);
                return Result<AgentParams>.Failure(new[] { "Erro ao deletar parâmetros." });
            }
        }

        public async Task<Result<IEnumerable<AgentParams>>> GetAll(int userId)
        {
            try
            {
                var paramsList = await _agentParamsRepository.GetByUserIdAsync(userId);
                if (!paramsList.Any())
                {
                    return Result<IEnumerable<AgentParams>>.Failure(new[] { "Nenhum parâmetro encontrado." });
                }

                _logger.LogInformation("Parâmetros recuperados com sucesso para o usuário: {UserId}", userId);
                return Result<IEnumerable<AgentParams>>.Success(paramsList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros para o usuário: {UserId}", userId);
                return Result<IEnumerable<AgentParams>>.Failure(new[] { "Erro ao recuperar parâmetros." });
            }
        }

        public async Task<Result<AgentParams>> GetOneById(int id)
        {
            try
            {
                var agentParams = await _agentParamsRepository.GetByIdAsync(id);
                if (agentParams == null)
                {
                    return Result<AgentParams>.Failure(new[] { "Parâmetros não encontrados." });
                }

                return Result<AgentParams>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros com ID: {Id}", id);
                return Result<AgentParams>.Failure(new[] { "Erro ao recuperar parâmetros." });
            }
        }

        public async Task<Result<AgentParams>> Update(AgentParamsDto data, int id)
        {
            try
            {
                var existing = await _agentParamsRepository.GetByIdAsync(id);
                if (existing == null)
                    return Result<AgentParams>.Failure(new[] { "Parâmetros não encontrados." });

                if (!string.IsNullOrWhiteSpace(data.ParamName))
                    existing.ParamName = data.ParamName;

                if (!string.IsNullOrWhiteSpace(data.CompanyName))
                    existing.CompanyName = data.CompanyName;

                if (!string.IsNullOrWhiteSpace(data.CompanyDescription))
                    existing.CompanyDescription = data.CompanyDescription;

                if (!string.IsNullOrWhiteSpace(data.Tone))
                    existing.Tone = data.Tone;

                if (!string.IsNullOrWhiteSpace(data.Goals))
                    existing.Goals = data.Goals;

                if (!string.IsNullOrWhiteSpace(data.MainMission))
                    existing.MainMission = data.MainMission;

                if (!string.IsNullOrWhiteSpace(data.ContextDescription))
                    existing.ContextDescription = data.ContextDescription;

                if (!string.IsNullOrWhiteSpace(data.QualificationRules))
                    existing.QualificationRules = data.QualificationRules;

                if (!string.IsNullOrWhiteSpace(data.ConversationGuidelines))
                    existing.ConversationGuidelines = data.ConversationGuidelines;

                if (!string.IsNullOrWhiteSpace(data.OpeningScript))
                    existing.OpeningScript = data.OpeningScript;

                if (!string.IsNullOrWhiteSpace(data.PreQualificationQuestions))
                    existing.PreQualificationQuestions = data.PreQualificationQuestions;

                if (!string.IsNullOrWhiteSpace(data.PainAgitationScript))
                    existing.PainAgitationScript = data.PainAgitationScript;

                if (!string.IsNullOrWhiteSpace(data.PricingAgitationScript))
                    existing.PricingAgitationScript = data.PricingAgitationScript;

                if (!string.IsNullOrWhiteSpace(data.TraditionalMethods))
                    existing.TraditionalMethods = data.TraditionalMethods;

                if (!string.IsNullOrWhiteSpace(data.SolutionScript))
                    existing.SolutionScript = data.SolutionScript;

                if (!string.IsNullOrWhiteSpace(data.ValueGenerationScript))
                    existing.ValueGenerationScript = data.ValueGenerationScript;

                if (!string.IsNullOrWhiteSpace(data.FinalQualificationQuestions))
                    existing.FinalQualificationQuestions = data.FinalQualificationQuestions;

                if (!string.IsNullOrWhiteSpace(data.OpportunityReinforcementScript))
                    existing.OpportunityReinforcementScript = data.OpportunityReinforcementScript;

                if (!string.IsNullOrWhiteSpace(data.EmotionalActivationScript))
                    existing.EmotionalActivationScript = data.EmotionalActivationScript;

                if (!string.IsNullOrWhiteSpace(data.CallToActionScript))
                    existing.CallToActionScript = data.CallToActionScript;

                if (!string.IsNullOrWhiteSpace(data.DisqualifiedFlowScript))
                    existing.DisqualifiedFlowScript = data.DisqualifiedFlowScript;

                if (!string.IsNullOrWhiteSpace(data.RestrictionsAndLimits))
                    existing.RestrictionsAndLimits = data.RestrictionsAndLimits;

                if (!string.IsNullOrWhiteSpace(data.AskAvailabilityStyle))
                    existing.AskAvailabilityStyle = data.AskAvailabilityStyle;

                if (!string.IsNullOrWhiteSpace(data.ConfirmationStyle))
                    existing.ConfirmationStyle = data.ConfirmationStyle;

                if (!string.IsNullOrWhiteSpace(data.UserTone))
                    existing.UserTone = data.UserTone;

                if (!string.IsNullOrWhiteSpace(data.AlternativeSuggestionStyle))
                    existing.AlternativeSuggestionStyle = data.AlternativeSuggestionStyle;

                if (!string.IsNullOrWhiteSpace(data.ReminderStyle))
                    existing.ReminderStyle = data.ReminderStyle;

                if (!string.IsNullOrWhiteSpace(data.ReminderTiming))
                    existing.ReminderTiming = data.ReminderTiming;

                if (!string.IsNullOrWhiteSpace(data.RecurrenceStyle))
                    existing.RecurrenceStyle = data.RecurrenceStyle;

                if (!string.IsNullOrWhiteSpace(data.CallToAction))
                    existing.CallToAction = data.CallToAction;

                if (!string.IsNullOrWhiteSpace(data.CourtesyMessage))
                    existing.CourtesyMessage = data.CourtesyMessage;

                if (!string.IsNullOrWhiteSpace(data.ParamName))
                    existing.ParamName = data.ParamName;

                var result = await _agentParamsRepository.UpdateAsync(existing);

                _logger.LogInformation("Parâmetros atualizados com sucesso. ID: {Id}", id);
                return Result<AgentParams>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao atualizar parâmetros com ID: {Id}", id);
                return Result<AgentParams>.Failure(new[] { "Erro ao atualizar parâmetros." });
            }
        }

        public async Task<Result<PaginatedResultParams<AgentParams>>> GetAgentParamsPaginated(AgentParamsPaginationRequestDto request)
        {
            try
            {
                _logger.LogInformation("Tentando recuperar parâmetros paginados para o usuário: {UserId}, página: {Page}, tamanho: {PageSize}",
                    request.UserId, request.Page, request.PageSize);

                var result = await _agentParamsRepository.GetAgentParamsPaginatedAsync(request);

                _logger.LogInformation("Parâmetros paginados recuperados com sucesso para o usuário: {UserId}. Total: {TotalCount}",
                    request.UserId, result.TotalCount);

                return Result<PaginatedResultParams<AgentParams>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao recuperar parâmetros paginados para o usuário: {UserId}", request.UserId);
                return Result<PaginatedResultParams<AgentParams>>.Failure(new[] { "Erro ao recuperar parâmetros paginados." });
            }
        }

        public async Task<Result<IEnumerable<AgentParams>>> SearchAgentParamsByName(AgentParamsSearchRequestDto request)
        {
            try
            {
                _logger.LogInformation("Tentando buscar parâmetros por nome: {ParamName} para o usuário: {UserId}",
                    request.ParamName, request.UserId);

                var agentParams = await _agentParamsRepository.SearchAgentParamsByNameAsync(request);

                _logger.LogInformation("Busca de parâmetros por nome concluída para o usuário: {UserId}. Encontrados: {Count} parâmetros",
                    request.UserId, agentParams.Count());

                return Result<IEnumerable<AgentParams>>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar parâmetros por nome: {ParamName} para o usuário: {UserId}",
                    request.ParamName, request.UserId);
                return Result<IEnumerable<AgentParams>>.Failure(new[] { "Erro ao buscar parâmetros por nome." });
            }
        }

        public async Task<Result<AgentParams>> GetAgentParamsByName(string paramName, int userId)
        {
            try
            {
                _logger.LogInformation("Tentando buscar parâmetro específico por nome: {ParamName} para o usuário: {UserId}",
                    paramName, userId);

                var agentParams = await _agentParamsRepository.GetAgentParamsByNameAsync(paramName, userId);

                if (agentParams == null)
                {
                    _logger.LogInformation("Parâmetro não encontrado com nome: {ParamName} para o usuário: {UserId}",
                        paramName, userId);
                    return Result<AgentParams>.Failure(new[] { "Parâmetro não encontrado com este nome." });
                }

                _logger.LogInformation("Parâmetro encontrado com nome: {ParamName} para o usuário: {UserId}",
                    paramName, userId);

                return Result<AgentParams>.Success(agentParams);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao buscar parâmetro por nome: {ParamName} para o usuário: {UserId}",
                    paramName, userId);
                return Result<AgentParams>.Failure(new[] { "Erro ao buscar parâmetro por nome." });
            }
        }
    }
}
