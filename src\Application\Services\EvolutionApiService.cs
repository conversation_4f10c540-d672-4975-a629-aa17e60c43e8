using SocketIOClient;
using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace HighAgentsApi.Application.Services
{
    public class EvolutionApiService : IEvolutionApiService
    {
        private readonly ILogger<EvolutionApiService> _logger;
        private readonly ConcurrentDictionary<string, SocketIOClient.SocketIO> _connections = new();
        private readonly ConcurrentDictionary<string, bool> _connectionStatus = new();

        public event EventHandler<EvolutionEventDto>? OnEventReceived;
        public event EventHandler<string>? OnConnectionStatusChanged;

        public EvolutionApiService(ILogger<EvolutionApiService> logger)
        {
            _logger = logger;
        }

        public async Task<WebhookStatusResponseDto> StartConnectionAsync(WebhookConnectionRequestDto request)
        {
            try
            {
                _logger.LogInformation("🚀 Iniciando conexão Evolution API para instância: {InstanceName}", request.InstanceName);

                // Verificar se já existe conexão ativa
                if (_connections.ContainsKey(request.InstanceName))
                {
                    _logger.LogWarning("⚠️ Conexão já existe para instância: {InstanceName}", request.InstanceName);
                    return new WebhookStatusResponseDto
                    {
                        Success = true,
                        InstanceName = request.InstanceName,
                        IsConnected = _connectionStatus.GetValueOrDefault(request.InstanceName, false),
                        Status = "Already Connected",
                        Timestamp = DateTime.UtcNow
                    };
                }

                // Construir possíveis URLs de conexão (tentativa com instância e fallback sem instância)
                var candidateUrls = new List<string>();
                var trimmedBase = request.ServerUrl?.TrimEnd('/') ?? string.Empty;
                if (!string.IsNullOrWhiteSpace(request.InstanceName))
                {
                    candidateUrls.Add($"{trimmedBase}");
                }
                candidateUrls.Add(trimmedBase);

                Exception? lastError = null;

                foreach (var connectionUrl in candidateUrls)
                {
                    _logger.LogInformation("📡 Tentando conectar em: {ConnectionUrl}", connectionUrl);

                    // Configurar Socket.IO
                    var socket = new SocketIOClient.SocketIO(connectionUrl, new SocketIOOptions
                    {
                        Transport = SocketIOClient.Transport.TransportProtocol.WebSocket,
                        ReconnectionDelay = request.ReconnectionDelay,
                        ReconnectionAttempts = request.ReconnectionAttempts
                    });

                    // Configurar event listeners
                    SetupEventListeners(socket, request.InstanceName);

                    // Aguardar desfecho de conexão (sucesso/erro) com timeout
                    var tcs = new TaskCompletionSource<bool>(TaskCreationOptions.RunContinuationsAsynchronously);
                    Exception? connectError = null;

                    void OnTempConnected(object? s, EventArgs e)
                    {
                        tcs.TrySetResult(true);
                    }

                    void OnTempError(object? s, string e)
                    {
                        connectError = new Exception(e);
                        tcs.TrySetException(connectError);
                    }

                    // Handlers temporários apenas para decisão de tentativa
                    socket.OnConnected += OnTempConnected;
                    socket.OnError += (sender, e) => OnTempError(sender, e);

                    using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(request.Timeout));
                    try
                    {
                        var connectTask = socket.ConnectAsync();
                        using (cts.Token.Register(() => tcs.TrySetCanceled()))
                        {
                            await Task.WhenAny(connectTask, tcs.Task);
                        }

                        // Se completou pelo evento de conectado
                        if (tcs.Task.IsCompletedSuccessfully && tcs.Task.Result)
                        {
                            // Armazenar conexão
                            _connections.TryAdd(request.InstanceName, socket);
                            _connectionStatus.TryAdd(request.InstanceName, true);

                            _logger.LogInformation("✅ Conexão estabelecida com sucesso para instância: {InstanceName} em {Url}", request.InstanceName, connectionUrl);

                            return new WebhookStatusResponseDto
                            {
                                Success = true,
                                InstanceName = request.InstanceName,
                                IsConnected = true,
                                Status = "Connected",
                                Timestamp = DateTime.UtcNow
                            };
                        }

                        // Se houve erro capturado via evento
                        if (tcs.Task.IsFaulted)
                        {
                            lastError = tcs.Task.Exception?.InnerException ?? connectError ?? new Exception("Falha ao conectar (evento de erro)");
                        }
                        else if (tcs.Task.IsCanceled)
                        {
                            lastError = new TimeoutException($"Timeout de conexão após {request.Timeout}ms em {connectionUrl}");
                        }
                        else if (connectTask.IsFaulted)
                        {
                            lastError = connectTask.Exception?.InnerException ?? new Exception("Falha ao conectar (ConnectAsync)");
                        }
                        else
                        {
                            lastError = new Exception("Falha desconhecida na tentativa de conexão");
                        }

                        _logger.LogWarning(lastError, "⚠️ Falha ao conectar em {Url}. Tentando próximo candidato (se houver)...", connectionUrl);
                    }
                    finally
                    {
                        // Remover handlers temporários
                        socket.OnConnected -= OnTempConnected;
                        // Não há -= para OnError com lambda inline; fica até Dispose
                    }

                    try
                    {
                        await socket.DisconnectAsync();
                    }
                    catch { }
                    socket.Dispose();
                }

                // Se chegou aqui, todas as tentativas falharam
                throw lastError ?? new InvalidOperationException("Falha desconhecida ao conectar ao servidor Evolution API");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao conectar com Evolution API para instância: {InstanceName}", request.InstanceName);
                return new WebhookStatusResponseDto
                {
                    Success = false,
                    InstanceName = request.InstanceName,
                    IsConnected = false,
                    Status = "Error",
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow
                };
            }
        }

        public async Task<WebhookStatusResponseDto> StopConnectionAsync(string instanceName)
        {
            try
            {
                _logger.LogInformation("🔌 Parando conexão para instância: {InstanceName}", instanceName);

                if (_connections.TryRemove(instanceName, out var socket))
                {
                    await socket.DisconnectAsync();
                    socket.Dispose();
                    _connectionStatus.TryRemove(instanceName, out _);

                    _logger.LogInformation("✅ Conexão encerrada com sucesso para instância: {InstanceName}", instanceName);

                    return new WebhookStatusResponseDto
                    {
                        Success = true,
                        InstanceName = instanceName,
                        IsConnected = false,
                        Status = "Disconnected",
                        Timestamp = DateTime.UtcNow
                    };
                }

                return new WebhookStatusResponseDto
                {
                    Success = false,
                    InstanceName = instanceName,
                    IsConnected = false,
                    Status = "Not Found",
                    Error = "Conexão não encontrada",
                    Timestamp = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao parar conexão para instância: {InstanceName}", instanceName);
                return new WebhookStatusResponseDto
                {
                    Success = false,
                    InstanceName = instanceName,
                    IsConnected = false,
                    Status = "Error",
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow
                };
            }
        }

        public Task<WebhookStatusResponseDto> GetConnectionStatusAsync(string instanceName)
        {
            var isConnected = _connectionStatus.GetValueOrDefault(instanceName, false);

            return Task.FromResult(new WebhookStatusResponseDto
            {
                Success = true,
                InstanceName = instanceName,
                IsConnected = isConnected,
                Status = isConnected ? "Connected" : "Disconnected",
                Timestamp = DateTime.UtcNow
            });
        }

        public Task<List<string>> GetActiveConnectionsAsync()
        {
            var activeConnections = _connectionStatus
                .Where(kvp => kvp.Value)
                .Select(kvp => kvp.Key)
                .ToList();

            return Task.FromResult(activeConnections);
        }

        private void SetupEventListeners(SocketIOClient.SocketIO socket, string instanceName)
        {
            // Evento: Conexão estabelecida
            socket.OnConnected += (sender, e) =>
            {
                _connectionStatus[instanceName] = true;
                _logger.LogInformation("✅ Conexão estabelecida para instância: {InstanceName}", instanceName);
                OnConnectionStatusChanged?.Invoke(this, $"Connected:{instanceName}");
            };

            // Evento: Desconexão
            socket.OnDisconnected += (sender, e) =>
            {
                _connectionStatus[instanceName] = false;
                _logger.LogWarning("🔌 Desconectado instância: {InstanceName}, Motivo: {Reason}", instanceName, e);
                OnConnectionStatusChanged?.Invoke(this, $"Disconnected:{instanceName}");
            };

            // Evento: Erro de conexão
            socket.OnError += (sender, e) =>
            {
                _logger.LogError("❌ Erro de conexão para instância: {InstanceName}, Erro: {Error}", instanceName, e);
                OnConnectionStatusChanged?.Invoke(this, $"Error:{instanceName}");
            };

            // Evento: Reconexão
            socket.OnReconnected += (sender, e) =>
            {
                _connectionStatus[instanceName] = true;
                _logger.LogInformation("🔄 Reconectado instância: {InstanceName} após {Attempts} tentativas", instanceName, e);
                OnConnectionStatusChanged?.Invoke(this, $"Reconnected:{instanceName}");
            };

            // Configurar listeners para eventos da Evolution API
            SetupEvolutionEventListeners(socket, instanceName);
        }

        private void SetupEvolutionEventListeners(SocketIOClient.SocketIO socket, string instanceName)
        {
            // Lista de eventos comuns da Evolution API
            var evolutionEvents = new[]
            {
                "message.upsert", "message.update", "message.delete",
                "chat.upsert", "chat.update", "chat.delete",
                "contact.upsert", "contact.update",
                "presence.update",
                "group.upsert", "group.update", "group.participants.update",
                "connection.update", "qr.updated", "qr.code",
                "status.instance", "status.find",
                "webhook.message", "webhook.status",
                "call.upsert", "call.update",
                "labels.edit", "labels.association"
            };

            // Registrar listeners para eventos conhecidos
            foreach (var eventName in evolutionEvents)
            {
                socket.On(eventName, response =>
                {
                    HandleEvolutionEvent(eventName, response, instanceName);
                });
            }

            // Capturar eventos não listados
            socket.OnAny((eventName, response) =>
            {
                if (!evolutionEvents.Contains(eventName))
                {
                    HandleEvolutionEvent(eventName, response, instanceName);
                }
            });
        }

        private void HandleEvolutionEvent(string eventName, SocketIOResponse response, string instanceName)
        {
            try
            {
                _logger.LogInformation("📨 EVENTO EVOLUTION API RECEBIDO:");
                _logger.LogInformation("⏰ Timestamp: {Timestamp}", DateTime.UtcNow);
                _logger.LogInformation("🏷️  Evento: {EventName}", eventName);
                _logger.LogInformation("📱 Instância: {InstanceName}", instanceName);

                var eventData = new EvolutionEventDto
                {
                    EventName = eventName,
                    Data = response.GetValue<object>(),
                    Timestamp = DateTime.UtcNow
                };

                // Log dos dados
                var jsonData = JsonSerializer.Serialize(eventData.Data, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                _logger.LogInformation("📄 Dados: {Data}", jsonData);

                // Disparar evento
                OnEventReceived?.Invoke(this, eventData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro ao processar evento {EventName} para instância {InstanceName}", eventName, instanceName);
            }
        }

        public void Dispose()
        {
            foreach (var connection in _connections.Values)
            {
                connection?.Dispose();
            }
            _connections.Clear();
            _connectionStatus.Clear();
        }
    }
}
