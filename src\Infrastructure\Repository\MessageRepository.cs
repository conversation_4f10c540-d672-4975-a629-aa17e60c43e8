using HighAgentsApi.Domain.Interfaces.Repository;
using HighAgentsApi.Domain.Models;
using HighCapital.Core.Domain.HighAgents.Entities;
using HighCapital.Core.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace HighAgentsApi.Infrastructure.Repository
{
    public class MessageRepository : IMessageRepository
    {
        private readonly CoreDbContext _context;
        private readonly DbSet<Message> _dataset;

        public MessageRepository(CoreDbContext context)
        {
            _context = context;
            _dataset = _context.Set<Message>();
        }

        public async Task<IEnumerable<Message>> GetAllAsync()
        {
            return await _dataset.ToListAsync();
        }

        public async Task<Message?> GetByIdAsync(int id)
        {
            return await _dataset.FindAsync(id);
        }

        public async Task AddAsync(Message entity)
        {
            await _dataset.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Message entity)
        {
            _dataset.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Message entity)
        {
            _dataset.Remove(entity);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Message>> GetByAgentIdAsync(int agentId)
        {
            return await _dataset.Where(m => m.AgentId == agentId).ToListAsync();
        }

        public async Task DeleteByAgentIdAsync(int agentId)
        {
            var messages = await _dataset.Where(m => m.AgentId == agentId).ToListAsync();

            _dataset.RemoveRange(messages);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<Message>> GetByRoleAsync(int agentId, string role)
        {
            return await _dataset.Where(m => m.AgentId == agentId && m.Role == role).ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetRecentByAgentIdAsync(int agentId, int count)
        {
            return await _dataset
                .Where(m => m.AgentId == agentId)
                .OrderByDescending(m => m.Id)
                .Take(count)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetByConversationAsync(
            string conversationIdentificator,
            int agentId
        )
        {
            return await _dataset
                .Where(m =>
                    m.ConversationIdentificator == conversationIdentificator && m.AgentId == agentId
                )
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetByConversationIdentificatorAsync(
            string conversationIdentificator
        )
        {
            return await _dataset
                .Where(m => m.ConversationIdentificator == conversationIdentificator)
                .OrderBy(m => m.Id)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetByRoleAndConversationAsync(
            string conversationIdentificator,
            string role
        )
        {
            return await _dataset
                .Where(m =>
                    m.ConversationIdentificator == conversationIdentificator && m.Role == role
                )
                .OrderBy(m => m.Id)
                .ToListAsync();
        }

        public async Task<IEnumerable<Message>> GetRecentByConversationAsync(
            string conversationIdentificator,
            int count
        )
        {
            return await _dataset
                .Where(m => m.ConversationIdentificator == conversationIdentificator)
                .OrderByDescending(m => m.Id)
                .Take(count)
                .ToListAsync();
        }

        public async Task DeleteByConversationAsync(string conversationIdentificator)
        {
            var messages = await _dataset
                .Where(m => m.ConversationIdentificator == conversationIdentificator)
                .ToListAsync();

            _dataset.RemoveRange(messages);
            await _context.SaveChangesAsync();
        }

        public async Task<
            Dictionary<string, IEnumerable<Message>>
        > GetMessagesGroupedByConversationAsync(int agentId)
        {
            // Limitar o número de mensagens para evitar sobrecarga
            var messages = await _dataset
                .Where(m => m.AgentId == agentId)
                .OrderBy(m => m.ConversationIdentificator)
                .ThenBy(m => m.Id)
                .Take(1000) // Limite de 1000 mensagens para evitar sobrecarga
                .ToListAsync();

            return messages
                .GroupBy(m => m.ConversationIdentificator)
                .ToDictionary(g => g.Key, g => g.AsEnumerable());
        }

        public async Task<IEnumerable<ConversationModel>> GetConversationsByAgentIdAsync(
            int agentId
        )
        {
            return await _dataset
                .Where(m => m.AgentId == agentId && m.Username != null)
                .Select(m => new ConversationModel
                {
                    ConversationIdentificator = m.ConversationIdentificator,
                    Username = m.Username,
                    AgentId = agentId,
                })
                .Distinct()
                .OrderBy(c => c.ConversationIdentificator)
                .ToListAsync();
        }
    }
}
