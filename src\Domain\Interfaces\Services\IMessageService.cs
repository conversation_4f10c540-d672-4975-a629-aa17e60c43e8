using System.Text;
using HighAgentsApi.Domain.Dto;
using HighAgentsApi.Domain.Models;
using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IMessageService
    {
        Task<IEnumerable<MessageDtoList>> GetAllByAgentIdAsync(int agentId);
        Task<IEnumerable<MessageDtoList>> GetByRoleAsync(int agentId, string role);
        Task<IEnumerable<MessageDtoList>> GetRecentByAgentIdAsync(int agentId, int count);
        Task<string> CreateAsync(MessageDto message, string leadName);
        Task<bool> DeleteAsync(int messageId);
        Task<bool> DeleteAllByAgentIdAsync(int agentId);
        Task<List<(string Role, string Content)>> GetByConversationAsync(
            string conversationIdentificator,
            int agentId
        );

        // Novos métodos baseados apenas no conversationIdentificator
        Task<IEnumerable<MessageDtoList>> GetAllByConversationAsync(
            string conversationIdentificator
        );
        Task<IEnumerable<MessageDtoList>> GetByRoleAndConversationAsync(
            string conversationIdentificator,
            string role
        );
        Task<IEnumerable<MessageDtoList>> GetRecentByConversationAsync(
            string conversationIdentificator,
            int count
        );
        Task<bool> DeleteAllByConversationAsync(string conversationIdentificator);

        // Método para agrupar mensagens por conversa
        Task<MessagesGroupedByConversationDto> GetMessagesGroupedByConversationAsync(int agentId);

        // Método para buscar apenas os identificadores de conversa
        Task<IEnumerable<ConversationModel>> GetConversationsAsync(int agentId);
    }
}
