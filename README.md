﻿# High Agents API

API REST para criação e gerenciamento de agentes de conversação com IA, utilizando o poder da OpenAI para gerar respostas inteligentes e contextualizadas.

## ☁️ Serviço em Nuvem

O serviço está implantado e acessível nos seguintes endpoints:

-   **URL Base**: [http://**************](http://**************)
-   **Documentação (Swagger/Scalar)**: [http://**************/docs](http://**************/docs)

## ✨ Funcionalidades Principais

-   **Gerenciamento de Agentes**: Endpoints para CRUD (Criar, Ler, Atualizar, Deletar) de agentes.
-   **Processamento de Mensagens**: API para enviar e receber mensagens, com integração direta com a OpenAI.
-   **Configuração de CORS**: Pré-configurado para permitir requisições de `http://localhost:5173` e `http://localhost:3000`, facilitando o desenvolvimento de front-ends.
-   **Documentação de API**: Geração automática de documentação com Swagger e Scalar.

## 🛠️ Tecnologias Utilizadas

-   **.NET 9.0**: Plataforma de desenvolvimento.
-   **ASP.NET Core**: Framework para construção da API.
-   **Entity Framework Core**: ORM para interação com o banco de dados.
-   **PostgreSQL**: Banco de dados relacional.
-   **Docker**: Para containerização e deploy.
-   **OpenAI API**: Para geração de respostas inteligentes.
-   **Clean Architecture**: Padrão de arquitetura para organização do código.

## 🏗️ Arquitetura

O projeto segue os princípios da **Clean Architecture**, dividindo as responsabilidades em camadas:

-   **`Domain`**: Contém as entidades, enums e a lógica de negócio mais central.
-   **`Application`**: Orquestra os casos de uso e a lógica da aplicação.
-   **`Infrastructure`**: Implementa o acesso a dados (repositórios) e a integração com serviços externos (como a OpenAI).
-   **`Api`**: Camada de apresentação, responsável por expor os endpoints REST, configurar a injeção de dependência e o pipeline de requisições.

## 🚀 Como Executar Localmente

### Pré-requisitos

-   [.NET 9.0 SDK](https://dotnet.microsoft.com/download/dotnet/9.0)
-   [Docker](https://www.docker.com/products/docker-desktop/)

### 1. Configuração

Clone o repositório e configure as variáveis de ambiente necessárias no arquivo `src/Api/appsettings.Development.json`, como a `ConnectionString` do PostgreSQL e a `ApiKey` da OpenAI.
