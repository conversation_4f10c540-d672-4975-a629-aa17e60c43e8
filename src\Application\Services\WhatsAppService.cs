using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Domain.Responses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace HighAgentsApi.Application.Services
{
    public class WhatsAppService : IWhatsAppService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WhatsAppService> _logger;

        private readonly IAgentService _agentService;
        private readonly IConfiguration _configuration;

        public WhatsAppService(HttpClient httpClient, ILogger<WhatsAppService> logger, IConfiguration configuration, IAgentService agentService)
        {
            _httpClient = httpClient;
            _logger = logger;
            _configuration = configuration;
            _agentService = agentService;
        }

        public async Task<Result<WhatsAppResponseDto>> TestConnectionAsync()
        {
            try
            {
                _logger.LogInformation("Testando conexão com o serviço externo do WhatsApp");

                var baseUrl = _configuration["WhatsApp:HighAgentsMessageService"];
                var response = await _httpClient.GetAsync($"{baseUrl}/api/whatsapp/test");

                var content = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Resposta do teste de conexão - Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Teste de conexão com WhatsApp realizado com sucesso");

                    // Retornar o JSON direto da resposta
                    var result = new WhatsAppResponseDto
                    {
                        Success = true,
                        Data = string.IsNullOrWhiteSpace(content) ? null : JsonSerializer.Deserialize<object>(content),
                        Message = "Conexão testada com sucesso"
                    };

                    return Result<WhatsAppResponseDto>.Success(result);
                }

                _logger.LogWarning("Falha no teste de conexão com WhatsApp. Status: {StatusCode}", response.StatusCode);
                return Result<WhatsAppResponseDto>.Failure(new[] { "Falha na conexão com o serviço WhatsApp" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao testar conexão com o serviço WhatsApp");
                return Result<WhatsAppResponseDto>.Failure(new[] { "Erro interno ao testar conexão" });
            }
        }

        public async Task<Result<WhatsAppResponseDto>> SendMessageAsync(string instanceName, SendMessageRequestDto request)
        {
            try
            {
                _logger.LogInformation("Enviando mensagem via WhatsApp para instância: {InstanceName}, número: {Number}",
                    instanceName, request.Number);

                var baseUrl = _configuration["WhatsApp:HighAgentsMessageService"];
                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{baseUrl}/api/whatsapp/{instanceName}/send-message", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Resposta do envio de mensagem - Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, responseContent);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Mensagem enviada com sucesso via WhatsApp para: {Number}", request.Number);

                    // Retornar o JSON direto da resposta
                    var result = new WhatsAppResponseDto
                    {
                        Success = true,
                        Data = string.IsNullOrWhiteSpace(responseContent) ? null : JsonSerializer.Deserialize<object>(responseContent),
                        Message = "Mensagem enviada com sucesso"
                    };

                    return Result<WhatsAppResponseDto>.Success(result);
                }

                _logger.LogWarning("Falha ao enviar mensagem via WhatsApp. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, responseContent);
                return Result<WhatsAppResponseDto>.Failure(new[] { "Falha ao enviar mensagem via WhatsApp" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao enviar mensagem via WhatsApp para instância: {InstanceName}", instanceName);
                return Result<WhatsAppResponseDto>.Failure(new[] { "Erro interno ao enviar mensagem" });
            }
        }

        public async Task<Result<WhatsAppResponseDto>> CreateInstanceAsync(CreateInstanceRequestDto request)
        {
            try
            {
                _logger.LogInformation("Criando instância do WhatsApp: {InstanceName}", request.InstanceName);

                var baseUrl = _configuration["WhatsApp:HighAgentsMessageService"];
                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{baseUrl}/api/WhatsApp/create-instance", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Resposta do serviço externo - Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, responseContent);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Instância do WhatsApp criada com sucesso: {InstanceName}", request.InstanceName);

                    // Retornar o JSON direto da resposta
                    var result = new WhatsAppResponseDto
                    {
                        Success = true,
                        Data = string.IsNullOrWhiteSpace(responseContent) ? null : JsonSerializer.Deserialize<object>(responseContent),
                        Message = "Instância criada com sucesso"
                    };

                    var agentDto = new AgentUpdateDto
                    {
                        InstanceWhatsappName = request.InstanceName
                    };

                    await _agentService.Update(agentDto, request.AgentId);

                    return Result<WhatsAppResponseDto>.Success(result);
                }

                _logger.LogWarning("Falha ao criar instância do WhatsApp. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, responseContent);
                return Result<WhatsAppResponseDto>.Failure(new[] { "Falha ao criar instância do WhatsApp" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao criar instância do WhatsApp: {InstanceName}", request.InstanceName);
                return Result<WhatsAppResponseDto>.Failure(new[] { "Erro interno ao criar instância" });
            }
        }

        public async Task<Result<QrCodeResponseDto>> GetQrCodeAsync(string instanceName)
        {
            try
            {
                _logger.LogInformation("Buscando QR Code para instância: {InstanceName}", instanceName);

                var baseUrl = _configuration["WhatsApp:HighAgentsMessageService"];
                var response = await _httpClient.GetAsync($"{baseUrl}/api/whatsapp/{instanceName}/qrcode");
                var content = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("QR Code obtido com sucesso para instância: {InstanceName}", instanceName);

                    // Retornar o JSON formatado do QR Code
                    var result = new QrCodeResponseDto
                    {
                        Success = true,
                        QrCode = string.IsNullOrWhiteSpace(content) ? null : JsonSerializer.Deserialize<object>(content),
                        Message = "QR Code obtido com sucesso"
                    };

                    return Result<QrCodeResponseDto>.Success(result);
                }

                _logger.LogWarning("Falha ao obter QR Code. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, content);
                return Result<QrCodeResponseDto>.Failure(new[] { "Falha ao obter QR Code" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao obter QR Code para instância: {InstanceName}", instanceName);
                return Result<QrCodeResponseDto>.Failure(new[] { "Erro interno ao obter QR Code" });
            }
        }

        public async Task<Result<ConnectionStatusResponseDto>> GetConnectionStatusAsync(string instanceName)
        {
            try
            {
                _logger.LogInformation("Verificando status de conexão para instância: {InstanceName}", instanceName);

                var baseUrl = _configuration["WhatsApp:HighAgentsMessageService"];
                var response = await _httpClient.GetAsync($"{baseUrl}/api/whatsapp/{instanceName}/connection-status");
                var content = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("Resposta do status de conexão - Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Status de conexão obtido com sucesso para instância: {InstanceName}", instanceName);

                    // Retornar o JSON formatado do status
                    var result = new ConnectionStatusResponseDto
                    {
                        Success = true,
                        Status = string.IsNullOrWhiteSpace(content) ? null : JsonSerializer.Deserialize<object>(content),
                        Message = "Status obtido com sucesso"
                    };

                    return Result<ConnectionStatusResponseDto>.Success(result);
                }

                _logger.LogWarning("Falha ao obter status de conexão. Status: {StatusCode}, Response: {Response}",
                    response.StatusCode, content);
                return Result<ConnectionStatusResponseDto>.Failure(new[] { "Falha ao obter status de conexão" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao obter status de conexão para instância: {InstanceName}", instanceName);
                return Result<ConnectionStatusResponseDto>.Failure(new[] { "Erro interno ao obter status de conexão" });
            }
        }
    }
}
