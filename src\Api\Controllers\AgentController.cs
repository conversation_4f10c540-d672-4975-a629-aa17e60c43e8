using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;

namespace HighAgentsApi.Service.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class AgentController : ControllerBase
{
    private readonly IAgentService _agentService;

    public AgentController(IAgentService agentService)
    {
        _agentService = agentService;
    }


    [HttpPost]
    public async Task<IActionResult> Create([FromBody] AgentDto agent)
    {

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {

            var result = await this._agentService.Create(agent);


            return Ok(result);



        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { errors = ex });
        }

    }


    [HttpGet("userId/{id}")]
    public async Task<ActionResult> GetAll(int id)
    {
        var result = await _agentService.GetAll(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result.Value);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult> Get(int id)
    {
        var result = await _agentService.GetOneById(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        if (result.Value == null)
        {
            return NotFound(new { message = "Agent not found" });
        }

        return Ok(result.Value);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult> Update(int id, [FromBody] AgentUpdateDto request)
    {
        var result = await _agentService.Update(request, id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result);
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        var result = await _agentService.Delete(id);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result);
    }

    [HttpGet("paginated")]
    public async Task<ActionResult> GetPaginated([FromQuery] AgentPaginationRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _agentService.GetAgentsPaginated(request);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result.Value);
    }

    [HttpGet("search")]
    public async Task<ActionResult> SearchByName([FromQuery] AgentSearchRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _agentService.SearchAgentsByName(request);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result.Value);
    }

    [HttpGet("find-by-name")]
    public async Task<ActionResult> GetByName([FromQuery] string name, [FromQuery] int userId)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return BadRequest(new { errors = new[] { "Nome é obrigatório." } });
        }

        if (userId <= 0)
        {
            return BadRequest(new { errors = new[] { "UserId deve ser maior que zero." } });
        }

        var result = await _agentService.GetAgentByName(name, userId);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(result.Value);
    }
}

