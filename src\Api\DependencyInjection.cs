using HighAgentsApi.Api.Helpers;
using HighAgentsApi.Domain.Interfaces.Services;
using HighAgentsApi.Service.Services;
using HighAgentsApi.Application.Services;
using HighCapital.Core.Infrastructure.Database;
using HighCapital.Core.Dependencies;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;


namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddWebServices(this IHostApplicationBuilder builder)
    {

        builder.Services.AddOpenApiDocumentation();

        builder.Services.AddDbContext<CoreDbContext>(options =>
            options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));


        builder.Services.AddScoped<IAgentService, AgentService>();
        builder.Services.AddHttpContextAccessor();
        builder.Services.AddExceptionHandler<CustomExceptionHandler>();
        builder.Services.AddProblemDetails();

        // Configurar HttpClient para WhatsApp
        builder.Services.AddHttpClient<IWhatsAppService, WhatsAppService>(client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        });


        builder.Services.AddHealthChecks();

        builder.Services.Configure<ApiBehaviorOptions>(options =>
            options.SuppressModelStateInvalidFilter = true);

        builder.Services.AddEndpointsApiExplorer();


    }
}
