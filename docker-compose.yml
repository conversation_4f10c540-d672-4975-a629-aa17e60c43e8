services:
#   web:
#     build:
#       context: .
#       dockerfile: Dockerfile
#     ports:
#       - "5000:8080"
#     depends_on:
#       - postgres
#     environment:
#       - ASPNETCORE_ENVIRONMENT=Development
#       - ASPNETCORE_URLS=http://+:8080
#       - ConnectionStrings__HighAgentsApiDb=Host=postgres;Database=HighAgentsApiDb;Username=postgres;Password=postgres123
#       - SKIP_DB_INIT=false
#     networks:
#       - app-network

  postgresHighAgents:
    image: postgres:16-alpine
    environment:
      - POSTGRES_DB=mydatabase
      - POSTGRES_USER=myuser
      - POSTGRES_PASSWORD=mypassword
    ports:
      - "5431:5432"
    volumes:
      - postgres_highagents_data:/var/lib/postgresql/data
    networks:
      - app-network

volumes:
  postgres_data:
  postgres_highagents_data:

networks:
  app-network:
    driver: bridge
