using HighAgentsApi.Domain.Dtos;
using HighAgentsApi.Domain.Responses;
using HighCapital.Core.Domain.HighAgents.Entities;

namespace HighAgentsApi.Domain.Interfaces.Services
{
    public interface IAgentService
    {
        Task<Result<AgentResponseDto>> GetOneById(int agentId);
        Task<Result<IEnumerable<AgentResponseDto>>> GetAll(int userId);
        Task<Result<Agent>> Update(AgentUpdateDto agent, int agentId);
        Task<Result<Agent>> Create(AgentDto agent);
        Task<Result<Agent>> Delete(int agentId);
        Task<Result<PaginatedResult<Agent>>> GetAgentsPaginated(AgentPaginationRequestDto request);
        Task<Result<IEnumerable<Agent>>> SearchAgentsByName(AgentSearchRequestDto request);
        Task<Result<AgentResponseDto>> GetAgentByName(string name, int userId);
    }
}
